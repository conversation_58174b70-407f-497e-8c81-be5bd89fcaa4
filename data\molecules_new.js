/**
 * 分子数据定义文件
 * 包含各种分子的原子坐标和键连接信息
 * 增强版 - 包含更多种类的分子和更精细的结构
 */

// 全局定义MOLECULES对象
window.MOLECULES = {
    // 水分子 (H2O)
    water: {
        name: "水分子",
        formula: "H₂O",
        description: "水分子由一个氧原子和两个氢原子组成，是生命存在的基础。",
        atoms: [
            { symbol: "O", position: [0, 0, 0], color: 0xff0000 },
            { symbol: "H", position: [0.8, 0.6, 0], color: 0xffffff },
            { symbol: "H", position: [-0.8, 0.6, 0], color: 0xffffff }
        ],
        bonds: [
            { start: 0, end: 1 },
            { start: 0, end: 2 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.05,
            amplitude: 0.1
        }
    },

    // 甲烷 (CH4)
    methane: {
        name: "甲烷",
        formula: "CH₄",
        description: "甲烷是最简单的烷烃，由一个碳原子和四个氢原子组成，是天然气的主要成分。",
        atoms: [
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "H", position: [0.8, 0.8, 0.8], color: 0xffffff },
            { symbol: "H", position: [-0.8, -0.8, 0.8], color: 0xffffff },
            { symbol: "H", position: [0.8, -0.8, -0.8], color: 0xffffff },
            { symbol: "H", position: [-0.8, 0.8, -0.8], color: 0xffffff }
        ],
        bonds: [
            { start: 0, end: 1 },
            { start: 0, end: 2 },
            { start: 0, end: 3 },
            { start: 0, end: 4 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.04,
            amplitude: 0.12
        }
    },

    // 二氧化碳 (CO2)
    carbonDioxide: {
        name: "二氧化碳",
        formula: "CO₂",
        description: "二氧化碳是一种重要的温室气体，由一个碳原子和两个氧原子组成，呈线性结构。",
        atoms: [
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "O", position: [1.2, 0, 0], color: 0xff0000 },
            { symbol: "O", position: [-1.2, 0, 0], color: 0xff0000 }
        ],
        bonds: [
            { start: 0, end: 1, type: "double" },
            { start: 0, end: 2, type: "double" }
        ],
        animation: {
            type: "vibration",
            frequency: 0.06,
            amplitude: 0.08
        }
    }
};

// 添加一个简单的测试分子，确保对象可用
MOLECULES.test = {
    name: "测试分子",
    formula: "Test",
    description: "这是一个测试分子，用于验证MOLECULES对象是否正确加载。",
    atoms: [
        { symbol: "X", position: [0, 0, 0], color: 0xff00ff }
    ],
    bonds: []
};

// 输出调试信息
console.log("molecules.js已加载，MOLECULES对象已定义");
console.log("可用分子:", Object.keys(MOLECULES));
