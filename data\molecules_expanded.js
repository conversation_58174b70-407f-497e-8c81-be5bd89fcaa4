/**
 * 分子数据定义文件 - 扩展版
 * 包含更多种类的分子和更精细的结构
 * 大幅扩展分子种类，添加更多类型的分子和更精确的3D结构
 * 新增更多生物分子、药物分子和复杂结构
 */

// 使用全局变量定义分子数据
if (typeof window.MOLECULES === 'undefined') {
    window.MOLECULES = {};
    console.warn("MOLECULES对象未定义，创建新对象");
}

// 直接添加扩展分子到MOLECULES对象
(function() {
    // 扩展分子定义
    // ==================== 氨基酸 ====================

    // 色氨酸 (Tryptophan)
    window.MOLECULES.tryptophan = {
        name: "色氨酸",
        formula: "C₁₁H₁₂N₂O₂",
        description: "色氨酸是一种含有吲哚环的必需氨基酸，是蛋白质合成的重要组成部分，也是血清素的前体。",
        atoms: [
            // 主链部分
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1.2, 0.5, 0], color: 0x808080 }, // α-碳
            { symbol: "C", position: [2.2, -0.5, 0], color: 0x808080 }, // 羧基碳
            { symbol: "O", position: [3.4, -0.2, 0], color: 0xff0000 }, // 羧基氧
            { symbol: "O", position: [2.0, -1.8, 0], color: 0xff0000 }, // 羧基氧

            // 侧链部分 - 吲哚环
            { symbol: "C", position: [1.2, 2.0, 0], color: 0x808080 }, // β-碳
            { symbol: "C", position: [2.4, 2.8, 0], color: 0x808080 }, // 连接吲哚环的碳

            // 吲哚环 - 六元环
            { symbol: "C", position: [2.4, 4.2, 0], color: 0x808080 },
            { symbol: "C", position: [3.6, 5.0, 0], color: 0x808080 },
            { symbol: "C", position: [4.8, 4.2, 0], color: 0x808080 },
            { symbol: "C", position: [4.8, 2.8, 0], color: 0x808080 },
            { symbol: "C", position: [3.6, 2.0, 0], color: 0x808080 },

            // 吲哚环 - 五元环
            { symbol: "N", position: [3.6, 0.6, 0], color: 0x0000ff },
            { symbol: "C", position: [4.8, 0.0, 0], color: 0x808080 },
            { symbol: "C", position: [6.0, 0.8, 0], color: 0x808080 },
            { symbol: "C", position: [6.0, 2.2, 0], color: 0x808080 },

            // 氢原子 (简化，只添加关键的氢)
            { symbol: "H", position: [-0.8, 0.5, 0], color: 0xffffff }, // 氨基氢
            { symbol: "H", position: [0.0, -1.0, 0], color: 0xffffff }, // 氨基氢
            { symbol: "H", position: [1.2, 0.5, 1.0], color: 0xffffff }, // α-碳氢
            { symbol: "H", position: [2.8, -0.2, 1.0], color: 0xffffff }, // 吲哚环氮氢
        ],
        bonds: [
            // 主链键
            { start: 0, end: 1 }, // N-Cα
            { start: 1, end: 2 }, // Cα-C
            { start: 2, end: 3, type: "double" }, // C=O
            { start: 2, end: 4 }, // C-O

            // 侧链键
            { start: 1, end: 5 }, // Cα-Cβ
            { start: 5, end: 6 }, // Cβ-C

            // 吲哚环 - 六元环
            { start: 6, end: 7 },
            { start: 7, end: 8 },
            { start: 8, end: 9 },
            { start: 9, end: 10 },
            { start: 10, end: 11 },
            { start: 11, end: 6 },

            // 吲哚环 - 五元环
            { start: 11, end: 12 },
            { start: 12, end: 13 },
            { start: 13, end: 14 },
            { start: 14, end: 15 },
            { start: 15, end: 10 },

            // 氢键
            { start: 0, end: 16 },
            { start: 0, end: 17 },
            { start: 1, end: 18 },
            { start: 12, end: 19 }
        ],
        animation: {
            type: "molecularDynamics",
            frequency: 0.03,
            amplitude: 0.12,
            speed: 1.0
        }
    };

    // 组氨酸 (Histidine)
    window.MOLECULES.histidine = {
        name: "组氨酸",
        formula: "C₆H₉N₃O₂",
        description: "组氨酸是一种含有咪唑环的氨基酸，在蛋白质中常作为催化位点，参与多种酶的活性中心。",
        atoms: [
            // 主链部分
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1.2, 0.5, 0], color: 0x808080 }, // α-碳
            { symbol: "C", position: [2.2, -0.5, 0], color: 0x808080 }, // 羧基碳
            { symbol: "O", position: [3.4, -0.2, 0], color: 0xff0000 }, // 羧基氧
            { symbol: "O", position: [2.0, -1.8, 0], color: 0xff0000 }, // 羧基氧

            // 侧链部分 - 咪唑环
            { symbol: "C", position: [1.2, 2.0, 0], color: 0x808080 }, // β-碳
            { symbol: "C", position: [2.4, 2.8, 0], color: 0x808080 }, // 连接咪唑环的碳

            // 咪唑环
            { symbol: "N", position: [2.4, 4.2, 0], color: 0x0000ff },
            { symbol: "C", position: [3.6, 4.8, 0], color: 0x808080 },
            { symbol: "N", position: [4.6, 4.0, 0], color: 0x0000ff },
            { symbol: "C", position: [4.0, 2.8, 0], color: 0x808080 },

            // 氢原子 (简化)
            { symbol: "H", position: [-0.8, 0.5, 0], color: 0xffffff }, // 氨基氢
            { symbol: "H", position: [0.0, -1.0, 0], color: 0xffffff }, // 氨基氢
            { symbol: "H", position: [1.2, 0.5, 1.0], color: 0xffffff }, // α-碳氢
            { symbol: "H", position: [1.6, 4.8, 0], color: 0xffffff }, // 咪唑环氮氢
        ],
        bonds: [
            // 主链键
            { start: 0, end: 1 }, // N-Cα
            { start: 1, end: 2 }, // Cα-C
            { start: 2, end: 3, type: "double" }, // C=O
            { start: 2, end: 4 }, // C-O

            // 侧链键
            { start: 1, end: 5 }, // Cα-Cβ
            { start: 5, end: 6 }, // Cβ-C

            // 咪唑环
            { start: 6, end: 7 },
            { start: 7, end: 8 },
            { start: 8, end: 9 },
            { start: 9, end: 10 },
            { start: 10, end: 6 },

            // 氢键
            { start: 0, end: 11 },
            { start: 0, end: 12 },
            { start: 1, end: 13 },
            { start: 7, end: 14 }
        ],
        animation: {
            type: "molecularDynamics",
            frequency: 0.03,
            amplitude: 0.12,
            speed: 1.0
        }
    };

    // ==================== 核苷酸 ====================

    // 腺嘌呤 (Adenine)
    window.MOLECULES.adenine = {
        name: "腺嘌呤",
        formula: "C₅H₅N₅",
        description: "腺嘌呤是DNA和RNA中的一种嘌呤碱基，与胸腺嘧啶(DNA)或尿嘧啶(RNA)形成碱基对。",
        atoms: [
            // 嘌呤环系统 - 五元环
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1.2, 0.5, 0], color: 0x808080 },
            { symbol: "N", position: [2.2, -0.3, 0], color: 0x0000ff },
            { symbol: "C", position: [1.6, -1.5, 0], color: 0x808080 },
            { symbol: "C", position: [0.2, -1.3, 0], color: 0x808080 },

            // 嘌呤环系统 - 六元环
            { symbol: "C", position: [2.4, 1.7, 0], color: 0x808080 },
            { symbol: "N", position: [3.7, 1.5, 0], color: 0x0000ff },
            { symbol: "C", position: [4.0, 0.2, 0], color: 0x808080 },
            { symbol: "N", position: [3.4, -0.8, 0], color: 0x0000ff },

            // 氨基
            { symbol: "N", position: [2.1, 3.0, 0], color: 0x0000ff },

            // 氢原子 (简化)
            { symbol: "H", position: [-0.8, 0.5, 0], color: 0xffffff },
            { symbol: "H", position: [2.0, -2.5, 0], color: 0xffffff },
            { symbol: "H", position: [-0.5, -2.0, 0], color: 0xffffff },
            { symbol: "H", position: [5.0, 0.0, 0], color: 0xffffff },
            { symbol: "H", position: [1.2, 3.5, 0], color: 0xffffff },
            { symbol: "H", position: [3.0, 3.5, 0], color: 0xffffff }
        ],
        bonds: [
            // 五元环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 0 },

            // 六元环
            { start: 1, end: 5 },
            { start: 5, end: 6 },
            { start: 6, end: 7 },
            { start: 7, end: 8 },
            { start: 8, end: 2 },

            // 氨基
            { start: 5, end: 9 },

            // 氢键
            { start: 0, end: 10 },
            { start: 3, end: 11 },
            { start: 4, end: 12 },
            { start: 7, end: 13 },
            { start: 9, end: 14 },
            { start: 9, end: 15 }
        ],
        animation: {
            type: "conformationalChange",
            amplitude: 0.5,
            targetMode: "twist",
            speed: 0.5
        }
    };

    // 胸腺嘧啶 (Thymine)
    window.MOLECULES.thymine = {
        name: "胸腺嘧啶",
        formula: "C₅H₆N₂O₂",
        description: "胸腺嘧啶是DNA中的一种嘧啶碱基，与腺嘌呤形成碱基对。在RNA中，它被尿嘧啶替代。",
        atoms: [
            // 嘧啶环
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1.2, 0.5, 0], color: 0x808080 },
            { symbol: "N", position: [2.4, -0.2, 0], color: 0x0000ff },
            { symbol: "C", position: [2.4, -1.6, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, -2.3, 0], color: 0x808080 },
            { symbol: "C", position: [0, -1.4, 0], color: 0x808080 },

            // 羰基
            { symbol: "O", position: [3.5, -2.2, 0], color: 0xff0000 },
            { symbol: "O", position: [1.2, -3.7, 0], color: 0xff0000 },

            // 甲基
            { symbol: "C", position: [1.2, 2.0, 0], color: 0x808080 },

            // 氢原子 (简化)
            { symbol: "H", position: [-0.8, 0.5, 0], color: 0xffffff },
            { symbol: "H", position: [3.2, 0.3, 0], color: 0xffffff },
            { symbol: "H", position: [-1.0, -1.8, 0], color: 0xffffff },
            { symbol: "H", position: [0.2, 2.5, 0], color: 0xffffff },
            { symbol: "H", position: [1.8, 2.5, 0.7], color: 0xffffff },
            { symbol: "H", position: [1.8, 2.5, -0.7], color: 0xffffff }
        ],
        bonds: [
            // 嘧啶环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 5 },
            { start: 5, end: 0 },

            // 羰基
            { start: 3, end: 6, type: "double" },
            { start: 4, end: 7, type: "double" },

            // 甲基
            { start: 1, end: 8 },

            // 氢键
            { start: 0, end: 9 },
            { start: 2, end: 10 },
            { start: 5, end: 11 },
            { start: 8, end: 12 },
            { start: 8, end: 13 },
            { start: 8, end: 14 }
        ],
        animation: {
            type: "conformationalChange",
            amplitude: 0.5,
            targetMode: "twist",
            speed: 0.5
        }
    };

    // ==================== 神经递质 ====================

    // 多巴胺 (Dopamine)
    window.MOLECULES.dopamine = {
        name: "多巴胺",
        formula: "C₈H₁₁NO₂",
        description: "多巴胺是一种重要的神经递质，参与大脑的奖励、动机和运动控制等功能。",
        atoms: [
            // 苯环
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, 0.7, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, 0, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, -1.4, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, -2.1, 0], color: 0x808080 },
            { symbol: "C", position: [0, -1.4, 0], color: 0x808080 },

            // 羟基
            { symbol: "O", position: [1.2, 2.1, 0], color: 0xff0000 },
            { symbol: "O", position: [3.6, 0.7, 0], color: 0xff0000 },

            // 乙胺侧链
            { symbol: "C", position: [-1.2, 0.7, 0], color: 0x808080 },
            { symbol: "C", position: [-2.4, 0, 0], color: 0x808080 },
            { symbol: "N", position: [-3.6, 0.7, 0], color: 0x0000ff },

            // 氢原子 (简化)
            { symbol: "H", position: [2.4, -2.2, 0.7], color: 0xffffff },
            { symbol: "H", position: [1.2, -3.2, 0], color: 0xffffff },
            { symbol: "H", position: [-0.8, -2.0, 0], color: 0xffffff },
            { symbol: "H", position: [2.1, 2.5, 0], color: 0xffffff },
            { symbol: "H", position: [4.2, 0.0, 0], color: 0xffffff }
        ],
        bonds: [
            // 苯环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 5 },
            { start: 5, end: 0 },

            // 羟基
            { start: 1, end: 6 },
            { start: 2, end: 7 },

            // 乙胺侧链
            { start: 0, end: 8 },
            { start: 8, end: 9 },
            { start: 9, end: 10 },

            // 氢键
            { start: 3, end: 11 },
            { start: 4, end: 12 },
            { start: 5, end: 13 },
            { start: 6, end: 14 },
            { start: 7, end: 15 }
        ],
        animation: {
            type: "molecularDynamics",
            frequency: 0.05,
            amplitude: 0.15,
            speed: 1.2
        }
    };

    // ==================== 脂质 ====================

    // 胆固醇 (Cholesterol) - 简化版
    window.MOLECULES.cholesterol = {
        name: "胆固醇",
        formula: "C₂₇H₄₆O",
        description: "胆固醇是细胞膜的重要组成部分，也是许多激素的前体。这是一个简化版的胆固醇模型。",
        atoms: [
            // 环系统 (简化为三个主要环)
            // 第一个环
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, 0.7, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, 0, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, -1.4, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, -2.1, 0], color: 0x808080 },
            { symbol: "C", position: [0, -1.4, 0], color: 0x808080 },

            // 第二个环
            { symbol: "C", position: [3.6, 0.7, 0], color: 0x808080 },
            { symbol: "C", position: [4.8, 0, 0], color: 0x808080 },
            { symbol: "C", position: [4.8, -1.4, 0], color: 0x808080 },
            { symbol: "C", position: [3.6, -2.1, 0], color: 0x808080 },

            // 第三个环
            { symbol: "C", position: [6.0, 0.7, 0], color: 0x808080 },
            { symbol: "C", position: [7.2, 0, 0], color: 0x808080 },
            { symbol: "C", position: [7.2, -1.4, 0], color: 0x808080 },
            { symbol: "C", position: [6.0, -2.1, 0], color: 0x808080 },

            // 侧链
            { symbol: "C", position: [8.4, 0.7, 0], color: 0x808080 },
            { symbol: "C", position: [9.6, 0, 0], color: 0x808080 },
            { symbol: "C", position: [10.8, 0.7, 0], color: 0x808080 },
            { symbol: "C", position: [12.0, 0, 0], color: 0x808080 },

            // 羟基
            { symbol: "O", position: [-1.2, 0.7, 0], color: 0xff0000 },

            // 甲基 (简化，只显示几个关键甲基)
            { symbol: "C", position: [1.2, 2.2, 0], color: 0x808080 },
            { symbol: "C", position: [3.6, 2.2, 0], color: 0x808080 },
            { symbol: "C", position: [6.0, 2.2, 0], color: 0x808080 }
        ],
        bonds: [
            // 第一个环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 5 },
            { start: 5, end: 0 },

            // 第二个环
            { start: 2, end: 6 },
            { start: 6, end: 7 },
            { start: 7, end: 8 },
            { start: 8, end: 9 },
            { start: 9, end: 3 },

            // 第三个环
            { start: 7, end: 10 },
            { start: 10, end: 11 },
            { start: 11, end: 12 },
            { start: 12, end: 13 },
            { start: 13, end: 8 },

            // 侧链
            { start: 11, end: 14 },
            { start: 14, end: 15 },
            { start: 15, end: 16 },
            { start: 16, end: 17 },

            // 羟基
            { start: 0, end: 18 },

            // 甲基
            { start: 1, end: 19 },
            { start: 6, end: 20 },
            { start: 10, end: 21 }
        ],
        animation: {
            type: "conformationalChange",
            amplitude: 0.8,
            targetMode: "fold",
            speed: 0.3
        }
    };

    // 磷脂 (Phospholipid) - 简化版
    window.MOLECULES.phospholipid = {
        name: "磷脂",
        formula: "简化结构",
        description: "磷脂是细胞膜的主要成分，具有亲水头部和疏水尾部。这是一个简化的磷脂模型。",
        atoms: [
            // 甘油骨架
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "C", position: [0, 1.4, 0], color: 0x808080 },
            { symbol: "C", position: [0, 2.8, 0], color: 0x808080 },

            // 磷酸基团
            { symbol: "O", position: [1.2, 3.5, 0], color: 0xff0000 },
            { symbol: "P", position: [2.4, 2.8, 0], color: 0xffa500 },
            { symbol: "O", position: [3.6, 3.5, 0], color: 0xff0000 },
            { symbol: "O", position: [2.4, 1.4, 0], color: 0xff0000 },
            { symbol: "O", position: [3.0, 2.2, 1.2], color: 0xff0000 },

            // 胆碱基团
            { symbol: "C", position: [4.8, 2.8, 0], color: 0x808080 },
            { symbol: "C", position: [6.0, 3.5, 0], color: 0x808080 },
            { symbol: "N", position: [7.2, 2.8, 0], color: 0x0000ff },
            { symbol: "C", position: [7.2, 1.4, 0], color: 0x808080 },
            { symbol: "C", position: [8.4, 3.5, 0], color: 0x808080 },
            { symbol: "C", position: [6.0, 2.1, 0], color: 0x808080 },

            // 脂肪酸尾链1
            { symbol: "O", position: [-1.2, -0.7, 0], color: 0xff0000 },
            { symbol: "C", position: [-2.4, 0, 0], color: 0x808080 },
            { symbol: "O", position: [-2.4, 1.4, 0], color: 0xff0000 },
            { symbol: "C", position: [-3.6, -0.7, 0], color: 0x808080 },
            { symbol: "C", position: [-4.8, 0, 0], color: 0x808080 },
            { symbol: "C", position: [-6.0, -0.7, 0], color: 0x808080 },
            { symbol: "C", position: [-7.2, 0, 0], color: 0x808080 },
            { symbol: "C", position: [-8.4, -0.7, 0], color: 0x808080 },

            // 脂肪酸尾链2
            { symbol: "O", position: [-1.2, 0.7, 1.4], color: 0xff0000 },
            { symbol: "C", position: [-2.4, 1.4, 1.4], color: 0x808080 },
            { symbol: "O", position: [-2.4, 2.8, 1.4], color: 0xff0000 },
            { symbol: "C", position: [-3.6, 0.7, 1.4], color: 0x808080 },
            { symbol: "C", position: [-4.8, 1.4, 1.4], color: 0x808080 },
            { symbol: "C", position: [-6.0, 0.7, 1.4], color: 0x808080 },
            { symbol: "C", position: [-7.2, 1.4, 1.4], color: 0x808080 }
        ],
        bonds: [
            // 甘油骨架
            { start: 0, end: 1 },
            { start: 1, end: 2 },

            // 磷酸基团连接
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 5 },
            { start: 4, end: 6 },
            { start: 4, end: 7 },

            // 胆碱基团
            { start: 5, end: 8 },
            { start: 8, end: 9 },
            { start: 9, end: 10 },
            { start: 10, end: 11 },
            { start: 10, end: 12 },
            { start: 10, end: 13 },

            // 脂肪酸尾链1
            { start: 0, end: 14 },
            { start: 14, end: 15 },
            { start: 15, end: 16, type: "double" },
            { start: 15, end: 17 },
            { start: 17, end: 18 },
            { start: 18, end: 19 },
            { start: 19, end: 20 },
            { start: 20, end: 21 },

            // 脂肪酸尾链2
            { start: 0, end: 22 },
            { start: 22, end: 23 },
            { start: 23, end: 24, type: "double" },
            { start: 23, end: 25 },
            { start: 25, end: 26 },
            { start: 26, end: 27 },
            { start: 27, end: 28 }
        ],
        animation: {
            type: "lipidWave",
            speed: 0.01,
            amplitude: 0.3
        }
    };

    // ==================== 碳水化合物 ====================

    // 葡萄糖 (Glucose) - α-D-葡萄糖
    window.MOLECULES.glucose = {
        name: "葡萄糖",
        formula: "C₆H₁₂O₆",
        description: "葡萄糖是最重要的单糖，是细胞能量代谢的主要燃料。这是α-D-葡萄糖的环状结构。",
        atoms: [
            // 吡喃环 (六元环)
            { symbol: "C", position: [0, 0, 0], color: 0x808080 }, // C1
            { symbol: "C", position: [1.2, -0.7, 0.5], color: 0x808080 }, // C2
            { symbol: "C", position: [0.7, -2.1, 0.5], color: 0x808080 }, // C3
            { symbol: "C", position: [-0.7, -2.1, -0.5], color: 0x808080 }, // C4
            { symbol: "C", position: [-1.2, -0.7, -0.5], color: 0x808080 }, // C5
            { symbol: "O", position: [-0.5, 0.7, -0.5], color: 0xff0000 }, // O环

            // 羟基
            { symbol: "O", position: [0.5, 0.7, 1.0], color: 0xff0000 }, // O1 (α位)
            { symbol: "O", position: [2.4, -0.7, -0.5], color: 0xff0000 }, // O2
            { symbol: "O", position: [1.4, -3.0, -0.5], color: 0xff0000 }, // O3
            { symbol: "O", position: [-1.4, -3.0, 0.5], color: 0xff0000 }, // O4
            { symbol: "O", position: [-2.4, -0.7, 0.5], color: 0xff0000 }, // O5

            // CH2OH基团
            { symbol: "C", position: [-0.7, 0.0, -2.0], color: 0x808080 }, // C6
            { symbol: "O", position: [-2.1, 0.0, -2.5], color: 0xff0000 }  // O6
        ],
        bonds: [
            // 吡喃环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 5 },
            { start: 5, end: 0 },

            // 羟基
            { start: 0, end: 6 },
            { start: 1, end: 7 },
            { start: 2, end: 8 },
            { start: 3, end: 9 },
            { start: 4, end: 10 },

            // CH2OH基团
            { start: 4, end: 11 },
            { start: 11, end: 12 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 1, 0],
            speed: 0.02
        }
    };

    // ==================== 抗生素 ====================

    // 青霉素G (Penicillin G) - 简化版
    window.MOLECULES.penicillinG = {
        name: "青霉素G",
        formula: "C₁₆H₁₈N₂O₄S",
        description: "青霉素G是第一个被发现的抗生素，通过抑制细菌细胞壁的合成来杀死细菌。",
        atoms: [
            // β-内酰胺环
            { symbol: "C", position: [0, 0, 0], color: 0x808080 }, // C2
            { symbol: "C", position: [1.2, 0.7, 0], color: 0x808080 }, // C3
            { symbol: "N", position: [2.4, 0, 0], color: 0x0000ff }, // N4
            { symbol: "C", position: [2.4, -1.4, 0], color: 0x808080 }, // C5
            { symbol: "O", position: [3.6, -2.1, 0], color: 0xff0000 }, // O=C

            // 噻唑烷环
            { symbol: "C", position: [1.2, -2.1, 0], color: 0x808080 }, // C6
            { symbol: "S", position: [1.2, -3.5, 0.7], color: 0xffff00 }, // S1
            { symbol: "C", position: [2.6, -3.5, 0], color: 0x808080 }, // C7

            // 羧基
            { symbol: "C", position: [0, -2.8, -1.0], color: 0x808080 }, // C8
            { symbol: "O", position: [0, -4.2, -1.0], color: 0xff0000 }, // O9
            { symbol: "O", position: [-1.0, -2.1, -1.7], color: 0xff0000 }, // O10

            // 苯基乙酰基侧链
            { symbol: "C", position: [0, 1.4, 0], color: 0x808080 }, // C11
            { symbol: "O", position: [-1.0, 2.1, 0.7], color: 0xff0000 }, // O12
            { symbol: "C", position: [1.2, 2.1, -0.7], color: 0x808080 }, // C13

            // 苯环
            { symbol: "C", position: [1.2, 3.5, -0.7], color: 0x808080 }, // C14
            { symbol: "C", position: [2.4, 4.2, -0.7], color: 0x808080 }, // C15
            { symbol: "C", position: [2.4, 5.6, -0.7], color: 0x808080 }, // C16
            { symbol: "C", position: [1.2, 6.3, -0.7], color: 0x808080 }, // C17
            { symbol: "C", position: [0, 5.6, -0.7], color: 0x808080 }, // C18
            { symbol: "C", position: [0, 4.2, -0.7], color: 0x808080 }  // C19
        ],
        bonds: [
            // β-内酰胺环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4, type: "double" },
            { start: 3, end: 5 },
            { start: 5, end: 0 },

            // 噻唑烷环
            { start: 5, end: 6 },
            { start: 6, end: 7 },
            { start: 7, end: 3 },

            // 羧基
            { start: 5, end: 8 },
            { start: 8, end: 9, type: "double" },
            { start: 8, end: 10 },

            // 苯基乙酰基侧链
            { start: 0, end: 11 },
            { start: 11, end: 12, type: "double" },
            { start: 11, end: 13 },

            // 苯环
            { start: 13, end: 14 },
            { start: 14, end: 15 },
            { start: 15, end: 16 },
            { start: 16, end: 17 },
            { start: 17, end: 18 },
            { start: 18, end: 14 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 1, 0],
            speed: 0.015
        }
    }
})();

// 输出调试信息
console.log("molecules_expanded.js已加载，扩展分子已添加到MOLECULES对象");
console.log("新增分子:", ["tryptophan", "histidine", "adenine", "thymine", "dopamine", "cholesterol", "phospholipid", "glucose", "penicillinG"].join(", "));
