/**
 * 分子数据定义文件
 * 包含各种分子的原子坐标和键连接信息
 * 增强版 - 包含更多种类的分子和更精细的结构
 */

// 使用全局变量定义分子数据
window.MOLECULES = {
    // 水分子 (H2O)
    water: {
        name: "水分子",
        formula: "H₂O",
        description: "水分子由一个氧原子和两个氢原子组成，是生命存在的基础。",
        atoms: [
            { symbol: "O", position: [0, 0, 0], color: 0xff0000 },
            { symbol: "H", position: [0.8, 0.6, 0], color: 0xffffff },
            { symbol: "H", position: [-0.8, 0.6, 0], color: 0xffffff }
        ],
        bonds: [
            { start: 0, end: 1 },
            { start: 0, end: 2 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.05,
            amplitude: 0.1
        }
    },

    // 甲烷 (CH4)
    methane: {
        name: "甲烷",
        formula: "CH₄",
        description: "甲烷是最简单的烷烃，由一个碳原子和四个氢原子组成，是天然气的主要成分。",
        atoms: [
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "H", position: [0.8, 0.8, 0.8], color: 0xffffff },
            { symbol: "H", position: [-0.8, -0.8, 0.8], color: 0xffffff },
            { symbol: "H", position: [0.8, -0.8, -0.8], color: 0xffffff },
            { symbol: "H", position: [-0.8, 0.8, -0.8], color: 0xffffff }
        ],
        bonds: [
            { start: 0, end: 1 },
            { start: 0, end: 2 },
            { start: 0, end: 3 },
            { start: 0, end: 4 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.04,
            amplitude: 0.12
        }
    },

    // 二氧化碳 (CO2)
    carbonDioxide: {
        name: "二氧化碳",
        formula: "CO₂",
        description: "二氧化碳是一种重要的温室气体，由一个碳原子和两个氧原子组成，呈线性结构。",
        atoms: [
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "O", position: [1.2, 0, 0], color: 0xff0000 },
            { symbol: "O", position: [-1.2, 0, 0], color: 0xff0000 }
        ],
        bonds: [
            { start: 0, end: 1, type: "double" },
            { start: 0, end: 2, type: "double" }
        ],
        animation: {
            type: "vibration",
            frequency: 0.06,
            amplitude: 0.08
        }
    },

    // 乙醇 (C2H5OH)
    ethanol: {
        name: "乙醇",
        formula: "C₂H₅OH",
        description: "乙醇是一种常见的醇类，是酒精饮料中的主要成分，也被用作消毒剂。",
        atoms: [
            // 第一个碳原子及其氢原子
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "H", position: [0, 0.9, 0.4], color: 0xffffff },
            { symbol: "H", position: [0, -0.5, 0.9], color: 0xffffff },
            { symbol: "H", position: [-0.9, -0.3, -0.3], color: 0xffffff },

            // 第二个碳原子及其氢原子
            { symbol: "C", position: [1.2, 0, -0.5], color: 0x808080 },
            { symbol: "H", position: [1.2, 0.9, -1.0], color: 0xffffff },
            { symbol: "H", position: [1.2, -0.9, -1.0], color: 0xffffff },

            // 羟基
            { symbol: "O", position: [2.4, 0, 0.2], color: 0xff0000 },
            { symbol: "H", position: [3.1, 0, -0.3], color: 0xffffff }
        ],
        bonds: [
            // 第一个碳的键
            { start: 0, end: 1 },
            { start: 0, end: 2 },
            { start: 0, end: 3 },
            { start: 0, end: 4 },

            // 第二个碳的键
            { start: 4, end: 5 },
            { start: 4, end: 6 },
            { start: 4, end: 7 },

            // 羟基键
            { start: 7, end: 8 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 1, 0],
            speed: 0.01
        }
    },

    // ATP分子 (简化版)
    atp: {
        name: "三磷酸腺苷 (ATP)",
        formula: "C₁₀H₁₆N₅O₁₃P₃",
        description: "ATP是细胞内能量传递的主要分子，通过水解释放能量。",
        atoms: [
            // 腺嘌呤部分 (简化)
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1, 0, 0], color: 0x808080 },
            { symbol: "N", position: [1.5, 1, 0], color: 0x0000ff },
            { symbol: "C", position: [0.5, 1.8, 0], color: 0x808080 },
            { symbol: "N", position: [-0.5, 1, 0], color: 0x0000ff },

            // 核糖部分 (简化)
            { symbol: "C", position: [2.5, -0.5, 0], color: 0x808080 },
            { symbol: "O", position: [3, 0.5, 0], color: 0xff0000 },
            { symbol: "C", position: [3.5, -1, 0.5], color: 0x808080 },

            // 三磷酸部分
            { symbol: "P", position: [4.5, -1.5, 0], color: 0xffa500 },
            { symbol: "O", position: [4.5, -2.5, 0.5], color: 0xff0000 },
            { symbol: "O", position: [4.5, -1.5, -1], color: 0xff0000 },
            { symbol: "P", position: [6, -1, 0], color: 0xffa500 },
            { symbol: "O", position: [6, -1, -1], color: 0xff0000 },
            { symbol: "O", position: [6.5, -2, 0.5], color: 0xff0000 },
            { symbol: "P", position: [7.5, -0.5, 0], color: 0xffa500 },
            { symbol: "O", position: [7.5, 0.5, 0.5], color: 0xff0000 },
            { symbol: "O", position: [8.5, -1, 0], color: 0xff0000 },
            { symbol: "O", position: [7, 0, -1], color: 0xff0000 }
        ],
        bonds: [
            // 腺嘌呤键
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 0 },

            // 连接到核糖
            { start: 1, end: 5 },
            { start: 5, end: 6 },
            { start: 5, end: 7 },

            // 三磷酸链
            { start: 7, end: 8 },
            { start: 8, end: 9 },
            { start: 8, end: 10 },
            { start: 8, end: 11 },
            { start: 11, end: 12 },
            { start: 11, end: 13 },
            { start: 11, end: 14 },
            { start: 14, end: 15 },
            { start: 14, end: 16 },
            { start: 14, end: 17 }
        ],
        animation: {
            type: "phosphateBreak",
            bondIndex: 13,
            speed: 0.02
        }
    },

    // DNA双螺旋 (改进版)
    dna: {
        name: "DNA双螺旋",
        formula: "C₃₃₇H₃₅₈N₁₂₄O₂₀₄P₃₂",
        description: "DNA是携带遗传信息的核酸，由两条互补的核苷酸链组成双螺旋结构。",
        // 动态生成螺旋结构
        generateStructure: function(turns = 2, basePairs = 10) {
            const atoms = [];
            const bonds = [];

            // 更准确的DNA参数
            const helixRadius = 10;           // 螺旋半径
            const helixRise = 3.4;            // 每个碱基对的上升高度
            const basesPerTurn = 10;          // 每转一圈的碱基对数量
            const angleStep = (Math.PI * 2) / basesPerTurn; // 每个碱基对的角度步进
            const backboneRadius = helixRadius - 1;  // 骨架半径略小于螺旋半径
            const baseDistance = 2;           // 碱基到骨架的距离

            // 存储碱基索引，用于后续连接
            const baseIndices = [];

            // 生成两条螺旋骨架
            for (let strand = 0; strand < 2; strand++) {
                const strandBaseIndices = [];
                const strandOffset = strand * Math.PI; // 两条链相对偏移180度

                // 每条链的方向相反
                const direction = strand === 0 ? 1 : -1;

                // 为每个碱基对生成结构
                for (let i = 0; i < basePairs; i++) {
                    // 计算当前碱基对的高度和角度
                    const height = i * helixRise;
                    const angle = i * angleStep + strandOffset;

                    // 计算骨架位置 (磷酸和糖)
                    const backboneX = backboneRadius * Math.cos(angle);
                    const backboneY = height;
                    const backboneZ = backboneRadius * Math.sin(angle);

                    // 添加磷酸基团
                    const phosphorusIndex = atoms.length;
                    atoms.push({
                        symbol: "P",
                        position: [backboneX, backboneY, backboneZ],
                        color: 0xffa500,
                        strand: strand
                    });

                    // 添加糖 (偏移一点位置)
                    const sugarOffset = 0.8; // 糖相对于磷酸的偏移
                    const sugarAngle = angle + (direction * 0.2); // 稍微偏移角度
                    const sugarX = backboneRadius * Math.cos(sugarAngle);
                    const sugarY = height + (direction * sugarOffset);
                    const sugarZ = backboneRadius * Math.sin(sugarAngle);

                    const sugarIndex = atoms.length;
                    atoms.push({
                        symbol: "O",
                        position: [sugarX, sugarY, sugarZ],
                        color: 0xff0000,
                        strand: strand
                    });

                    // 连接磷酸和糖
                    bonds.push({
                        start: phosphorusIndex,
                        end: sugarIndex
                    });

                    // 如果不是第一个碱基对，连接到前一个磷酸
                    if (i > 0) {
                        const prevPhosphorusIndex = phosphorusIndex - 2; // 前一个磷酸的索引
                        if (prevPhosphorusIndex >= 0) {
                            bonds.push({
                                start: prevPhosphorusIndex,
                                end: sugarIndex
                            });
                        }
                    }

                    // 添加碱基 (指向螺旋中心)
                    // 计算指向中心的向量
                    const toCenterX = -backboneX;
                    const toCenterZ = -backboneZ;
                    const vectorLength = Math.sqrt(toCenterX * toCenterX + toCenterZ * toCenterZ);
                    const normalizedX = toCenterX / vectorLength;
                    const normalizedZ = toCenterZ / vectorLength;

                    // 碱基位置 (沿着指向中心的方向)
                    const baseX = backboneX + normalizedX * baseDistance;
                    const baseY = backboneY;
                    const baseZ = backboneZ + normalizedZ * baseDistance;

                    // 根据链和位置确定碱基类型
                    let baseSymbol, baseColor;
                    if (strand === 0) {
                        baseSymbol = i % 2 === 0 ? "A" : "G";
                        baseColor = i % 2 === 0 ? 0x0000ff : 0x00ff00; // A蓝色，G绿色
                    } else {
                        baseSymbol = i % 2 === 0 ? "T" : "C";
                        baseColor = i % 2 === 0 ? 0xff0000 : 0xffff00; // T红色，C黄色
                    }

                    const baseIndex = atoms.length;
                    atoms.push({
                        symbol: baseSymbol,
                        position: [baseX, baseY, baseZ],
                        color: baseColor,
                        strand: strand,
                        pairIndex: i
                    });

                    // 连接糖到碱基
                    bonds.push({
                        start: sugarIndex,
                        end: baseIndex
                    });

                    // 存储碱基索引，用于后续连接碱基对
                    strandBaseIndices.push(baseIndex);
                }

                baseIndices.push(strandBaseIndices);
            }

            // 连接碱基对
            if (baseIndices.length === 2) {
                const strand1Bases = baseIndices[0];
                const strand2Bases = baseIndices[1];

                // 确保两条链的碱基数量相同
                const pairsCount = Math.min(strand1Bases.length, strand2Bases.length);

                for (let i = 0; i < pairsCount; i++) {
                    // 连接对应的碱基对
                    bonds.push({
                        start: strand1Bases[i],
                        end: strand2Bases[i], // 直接连接对应位置的碱基
                        color: 0xffffff,
                        dashed: true
                    });
                }
            }

            return { atoms, bonds };
        },
        animation: {
            type: "rotation",
            axis: [0, 1, 0],
            speed: 0.005
        }
    },

    // 蛋白质结构 (简化的α螺旋)
    protein: {
        name: "蛋白质α螺旋结构",
        formula: "多肽链",
        description: "α螺旋是蛋白质的常见二级结构，由氨基酸残基通过肽键连接形成。",
        // 动态生成α螺旋结构
        generateStructure: function(turns = 3) {
            const atoms = [];
            const bonds = [];
            const residuesPerTurn = 3.6;  // 每圈约3.6个氨基酸残基
            const helixRadius = 2.3;      // 螺旋半径
            const helixRise = 1.5;        // 每个残基的上升高度
            const totalResidues = Math.ceil(turns * residuesPerTurn);

            // 生成主链原子
            for (let i = 0; i < totalResidues; i++) {
                const angle = (i / residuesPerTurn) * Math.PI * 2;
                const height = i * helixRise;

                // 计算位置
                const x = helixRadius * Math.cos(angle);
                const y = height;
                const z = helixRadius * Math.sin(angle);

                // 添加α碳 (代表氨基酸残基)
                const carbonIndex = atoms.length;
                atoms.push({
                    symbol: "C",
                    position: [x, y, z],
                    color: 0x808080
                });

                // 添加氮原子 (肽键的一部分)
                const nitrogenIndex = atoms.length;
                atoms.push({
                    symbol: "N",
                    position: [
                        x + 0.5 * Math.cos(angle + 0.5),
                        y + 0.5,
                        z + 0.5 * Math.sin(angle + 0.5)
                    ],
                    color: 0x0000ff
                });

                // 添加氧原子 (肽键的一部分)
                const oxygenIndex = atoms.length;
                atoms.push({
                    symbol: "O",
                    position: [
                        x + 0.5 * Math.cos(angle - 0.5),
                        y - 0.5,
                        z + 0.5 * Math.sin(angle - 0.5)
                    ],
                    color: 0xff0000
                });

                // 添加侧链 (简化为一个原子)
                const sideChainIndex = atoms.length;
                atoms.push({
                    symbol: "R",
                    position: [
                        x - 1.5 * Math.cos(angle),
                        y,
                        z - 1.5 * Math.sin(angle)
                    ],
                    color: 0x00ff00
                });

                // 连接主链原子
                bonds.push({ start: carbonIndex, end: nitrogenIndex });
                bonds.push({ start: carbonIndex, end: oxygenIndex });
                bonds.push({ start: carbonIndex, end: sideChainIndex });

                // 连接到前一个残基
                if (i > 0) {
                    // 连接到前一个残基的氮原子
                    bonds.push({
                        start: carbonIndex - 4,  // 前一个碳原子
                        end: nitrogenIndex,
                        color: 0xffff00  // 肽键
                    });
                }
            }

            return { atoms, bonds };
        },
        animation: {
            type: "folding",
            speed: 0.01,
            maxAngle: Math.PI / 8
        }
    },
    // 甘氨酸 (最简单的氨基酸)
    glycine: {
        name: "甘氨酸",
        formula: "C₂H₅NO₂",
        description: "甘氨酸是最简单的氨基酸，侧链仅为一个氢原子。它在蛋白质中常作为柔性连接区域。",
        atoms: [
            // 主链部分
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1.2, 0, 0], color: 0x808080 }, // α碳
            { symbol: "C", position: [1.8, 1.3, 0], color: 0x808080 }, // 羧基碳
            { symbol: "O", position: [1.1, 2.3, 0], color: 0xff0000 }, // 羧基氧
            { symbol: "O", position: [3.1, 1.3, 0], color: 0xff0000 }, // 羧基氧

            // 侧链 (甘氨酸只有一个氢原子)
            { symbol: "H", position: [1.5, -0.5, 0.9], color: 0xffffff },

            // 氨基上的氢
            { symbol: "H", position: [-0.5, 0.8, 0], color: 0xffffff },
            { symbol: "H", position: [-0.5, -0.8, 0], color: 0xffffff }
        ],
        bonds: [
            // 主链连接
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 2, end: 4 },

            // 侧链连接
            { start: 1, end: 5 },

            // 氨基氢连接
            { start: 0, end: 6 },
            { start: 0, end: 7 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.03,
            amplitude: 0.08
        }
    },

    // 丙氨酸 (简单的疏水性氨基酸)
    alanine: {
        name: "丙氨酸",
        formula: "C₃H₇NO₂",
        description: "丙氨酸是一种简单的疏水性氨基酸，侧链为甲基。它是蛋白质中第二常见的氨基酸。",
        atoms: [
            // 主链部分
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1.2, 0, 0], color: 0x808080 }, // α碳
            { symbol: "C", position: [1.8, 1.3, 0], color: 0x808080 }, // 羧基碳
            { symbol: "O", position: [1.1, 2.3, 0], color: 0xff0000 }, // 羧基氧
            { symbol: "O", position: [3.1, 1.3, 0], color: 0xff0000 }, // 羧基氧

            // 侧链 (甲基)
            { symbol: "C", position: [1.5, -1.0, 1.0], color: 0x808080 },
            { symbol: "H", position: [0.8, -1.8, 1.2], color: 0xffffff },
            { symbol: "H", position: [2.0, -0.7, 1.9], color: 0xffffff },
            { symbol: "H", position: [2.2, -1.5, 0.4], color: 0xffffff },

            // 氨基上的氢
            { symbol: "H", position: [-0.5, 0.8, 0], color: 0xffffff },
            { symbol: "H", position: [-0.5, -0.8, 0], color: 0xffffff }
        ],
        bonds: [
            // 主链连接
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 2, end: 4 },

            // 侧链连接
            { start: 1, end: 5 },
            { start: 5, end: 6 },
            { start: 5, end: 7 },
            { start: 5, end: 8 },

            // 氨基氢连接
            { start: 0, end: 9 },
            { start: 0, end: 10 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.03,
            amplitude: 0.08
        }
    },

    // RNA分子 (简化版)
    rna: {
        name: "RNA分子",
        formula: "C₁₀H₁₂N₅O₇P",
        description: "RNA是单链核酸，在蛋白质合成和基因调控中起关键作用。",
        // 动态生成RNA结构
        generateStructure: function(length = 10) {
            const atoms = [];
            const bonds = [];
            const baseDistance = 2;
            const helixRadius = 8;
            const helixRise = 3;
            const angleStep = (Math.PI * 2) / 10;

            // 生成单链RNA骨架
            for (let i = 0; i < length * 10; i++) {
                const angle = i * angleStep * 0.8; // 不是完整的螺旋，更像是弯曲的链
                const height = (i / 10) * helixRise;

                // 磷酸和糖的位置
                const x = helixRadius * Math.cos(angle);
                const y = height;
                const z = helixRadius * Math.sin(angle);

                // 添加磷酸
                if (i % 10 === 0 && i > 0) {
                    const phosphorusIndex = atoms.length;
                    atoms.push({
                        symbol: "P",
                        position: [x, y, z],
                        color: 0xffa500
                    });

                    // 连接到前一个糖
                    if (phosphorusIndex > 0) {
                        bonds.push({
                            start: phosphorusIndex - 1,
                            end: phosphorusIndex
                        });
                    }
                }

                // 添加糖 (核糖)
                if (i % 10 === 5) {
                    const sugarIndex = atoms.length;
                    atoms.push({
                        symbol: "O",
                        position: [x, y, z],
                        color: 0xff0000
                    });

                    // 连接到前一个磷酸
                    if (sugarIndex > 0) {
                        bonds.push({
                            start: sugarIndex - 1,
                            end: sugarIndex
                        });
                    }

                    // 添加碱基 (简化为一个原子)
                    if (i < length * 10 - 5) {
                        const baseIndex = atoms.length;
                        const baseOffset = 4;

                        // 随机选择碱基类型 (A, U, G, C)
                        const baseTypes = ["A", "U", "G", "C"];
                        const baseType = baseTypes[Math.floor(Math.random() * baseTypes.length)];

                        // 根据碱基类型设置颜色
                        let baseColor;
                        switch (baseType) {
                            case "A": baseColor = 0x0000ff; break; // 腺嘌呤 - 蓝色
                            case "U": baseColor = 0xff00ff; break; // 尿嘧啶 - 粉色
                            case "G": baseColor = 0x00ff00; break; // 鸟嘌呤 - 绿色
                            case "C": baseColor = 0xffff00; break; // 胞嘧啶 - 黄色
                        }

                        atoms.push({
                            symbol: baseType,
                            position: [
                                x * (1 - baseOffset/helixRadius),
                                y,
                                z * (1 - baseOffset/helixRadius)
                            ],
                            color: baseColor
                        });

                        // 连接糖到碱基
                        bonds.push({
                            start: sugarIndex,
                            end: baseIndex
                        });
                    }
                }
            }

            return { atoms, bonds };
        },
        animation: {
            type: "waveMotion",
            speed: 0.01,
            amplitude: 0.5
        }
    },

    // 磷脂双分子层 (细胞膜的主要成分)
    phospholipidBilayer: {
        name: "磷脂双分子层",
        formula: "多个磷脂分子",
        description: "磷脂双分子层是细胞膜的基本结构，由两层磷脂分子排列形成，亲水头部朝外，疏水尾部朝内。",
        // 动态生成磷脂双分子层
        generateStructure: function(width = 5, height = 2) {
            const atoms = [];
            const bonds = [];
            const lipidSpacing = 3;

            // 生成上下两层磷脂
            for (let layer = 0; layer < 2; layer++) {
                const yOffset = layer === 0 ? 0 : 15; // 上下层的垂直间距
                const headDirection = layer === 0 ? -1 : 1; // 头部朝向 (上层朝上，下层朝下)

                for (let x = 0; x < width; x++) {
                    for (let z = 0; z < height; z++) {
                        // 计算这个磷脂分子的位置
                        const xPos = x * lipidSpacing;
                        const zPos = z * lipidSpacing;

                        // 添加磷脂头部 (磷酸基团)
                        const headIndex = atoms.length;
                        atoms.push({
                            symbol: "P",
                            position: [xPos, yOffset + headDirection * 2, zPos],
                            color: 0xffa500
                        });

                        // 添加甘油骨架
                        const glycerolIndex = atoms.length;
                        atoms.push({
                            symbol: "C",
                            position: [xPos, yOffset, zPos],
                            color: 0x808080
                        });

                        // 连接头部和甘油
                        bonds.push({
                            start: headIndex,
                            end: glycerolIndex
                        });

                        // 添加两条脂肪酸尾链
                        const tailLength = 5;
                        let prevTail1Index = glycerolIndex;
                        let prevTail2Index = glycerolIndex;

                        for (let t = 1; t <= tailLength; t++) {
                            // 第一条尾链
                            const tail1Index = atoms.length;
                            atoms.push({
                                symbol: "C",
                                position: [
                                    xPos - 0.5,
                                    yOffset - headDirection * t * 1.2,
                                    zPos
                                ],
                                color: 0x808080
                            });

                            // 连接到前一个碳原子
                            bonds.push({
                                start: prevTail1Index,
                                end: tail1Index
                            });

                            prevTail1Index = tail1Index;

                            // 第二条尾链
                            const tail2Index = atoms.length;
                            atoms.push({
                                symbol: "C",
                                position: [
                                    xPos + 0.5,
                                    yOffset - headDirection * t * 1.2,
                                    zPos
                                ],
                                color: 0x808080
                            });

                            // 连接到前一个碳原子
                            bonds.push({
                                start: prevTail2Index,
                                end: tail2Index
                            });

                            prevTail2Index = tail2Index;
                        }
                    }
                }
            }

            return { atoms, bonds };
        },
        animation: {
            type: "lipidWave",
            speed: 0.01,
            amplitude: 0.3
        }
    },

    // β折叠蛋白结构
    betaSheet: {
        name: "β折叠蛋白结构",
        formula: "多肽链",
        description: "β折叠是蛋白质的一种重要二级结构，由多条伸展的肽链通过氢键连接形成。",
        // 动态生成β折叠结构
        generateStructure: function(strands = 4, residuesPerStrand = 6) {
            const atoms = [];
            const bonds = [];
            const strandSpacing = 4.5;
            const residueSpacing = 3.5;

            // 生成多条β链
            for (let strand = 0; strand < strands; strand++) {
                const zPos = strand * strandSpacing;
                const direction = strand % 2 === 0 ? 1 : -1; // 相邻链方向相反

                for (let res = 0; res < residuesPerStrand; res++) {
                    // 计算这个残基在链上的位置
                    const xPos = direction === 1 ? res * residueSpacing : (residuesPerStrand - res - 1) * residueSpacing;

                    // 添加α碳 (代表氨基酸残基)
                    const carbonIndex = atoms.length;
                    atoms.push({
                        symbol: "C",
                        position: [xPos, 0, zPos],
                        color: 0x808080
                    });

                    // 添加氮原子 (肽键的一部分)
                    const nitrogenIndex = atoms.length;
                    atoms.push({
                        symbol: "N",
                        position: [xPos - direction * 1.0, 0, zPos],
                        color: 0x0000ff
                    });

                    // 添加氧原子 (肽键的一部分)
                    const oxygenIndex = atoms.length;
                    atoms.push({
                        symbol: "O",
                        position: [xPos, 1.0, zPos],
                        color: 0xff0000
                    });

                    // 添加侧链 (简化为一个原子)
                    const sideChainIndex = atoms.length;
                    atoms.push({
                        symbol: "R",
                        position: [xPos, -1.5, zPos],
                        color: 0x00ff00
                    });

                    // 连接主链原子
                    bonds.push({ start: carbonIndex, end: nitrogenIndex });
                    bonds.push({ start: carbonIndex, end: oxygenIndex });
                    bonds.push({ start: carbonIndex, end: sideChainIndex });

                    // 连接到前一个残基 (在同一条链上)
                    if (res > 0) {
                        const prevCarbonIndex = carbonIndex - 4;
                        bonds.push({
                            start: prevCarbonIndex,
                            end: nitrogenIndex,
                            color: 0xffff00 // 肽键
                        });
                    }

                    // 添加氢键连接相邻链 (β折叠的特征)
                    if (strand > 0 && res > 0 && res < residuesPerStrand - 1) {
                        // 找到上一条链对应位置的氧原子
                        const prevStrandOxygenIndex = oxygenIndex - 4 * residuesPerStrand;

                        if (prevStrandOxygenIndex >= 0) {
                            bonds.push({
                                start: nitrogenIndex,
                                end: prevStrandOxygenIndex,
                                color: 0x00ffff, // 氢键
                                dashed: true
                            });
                        }
                    }
                }
            }

            return { atoms, bonds };
        },
        animation: {
            type: "sheetWave",
            speed: 0.01,
            amplitude: 0.5
        }
    },

    // 酶-底物复合物 (简化版)
    enzymeSubstrate: {
        name: "酶-底物复合物",
        formula: "复合结构",
        description: "展示了酶与底物结合的关键过程，是理解酶催化机制的基础。",
        // 动态生成酶-底物复合物
        generateStructure: function() {
            const atoms = [];
            const bonds = [];

            // 生成酶活性位点 (简化为一个口袋结构)
            const pocketRadius = 8;
            const pocketDepth = 4;
            const pocketResidues = 12;

            // 创建活性位点口袋
            for (let i = 0; i < pocketResidues; i++) {
                const angle = (i / pocketResidues) * Math.PI * 2;
                const x = pocketRadius * Math.cos(angle);
                const z = pocketRadius * Math.sin(angle);

                // 添加口袋边缘的残基 (简化为一个原子)
                const residueIndex = atoms.length;
                atoms.push({
                    symbol: "C",
                    position: [x, 0, z],
                    color: 0x808080
                });

                // 添加催化残基 (在口袋内部)
                if (i % 4 === 0) {
                    const catalyticIndex = atoms.length;
                    atoms.push({
                        symbol: i % 8 === 0 ? "O" : "N", // 交替使用氧和氮代表不同类型的催化残基
                        position: [
                            x * 0.6,
                            -pocketDepth / 2,
                            z * 0.6
                        ],
                        color: i % 8 === 0 ? 0xff0000 : 0x0000ff
                    });

                    // 连接催化残基到口袋边缘
                    bonds.push({
                        start: residueIndex,
                        end: catalyticIndex
                    });
                }

                // 连接口袋边缘形成环
                if (i > 0) {
                    bonds.push({
                        start: residueIndex - 1,
                        end: residueIndex
                    });
                }
            }

            // 闭合环
            bonds.push({
                start: 0,
                end: atoms.length - 1
            });

            // 添加底物分子 (简化为一个小分子)
            const substrateStartIndex = atoms.length;

            // 底物中心
            atoms.push({
                symbol: "C",
                position: [0, -pocketDepth, 0],
                color: 0xffff00
            });

            // 底物周围的原子
            const substrateAtoms = 5;
            for (let i = 0; i < substrateAtoms; i++) {
                const angle = (i / substrateAtoms) * Math.PI * 2;
                const subAtomIndex = atoms.length;

                atoms.push({
                    symbol: i % 2 === 0 ? "O" : "C",
                    position: [
                        2 * Math.cos(angle),
                        -pocketDepth,
                        2 * Math.sin(angle)
                    ],
                    color: i % 2 === 0 ? 0xff0000 : 0x808080
                });

                // 连接到底物中心
                bonds.push({
                    start: substrateStartIndex,
                    end: subAtomIndex
                });

                // 找到最近的催化残基，添加相互作用
                let minDist = Infinity;
                let closestCatalytic = -1;

                for (let j = 0; j < substrateStartIndex; j++) {
                    if (atoms[j].symbol === "O" || atoms[j].symbol === "N") {
                        const dx = atoms[j].position[0] - atoms[subAtomIndex].position[0];
                        const dy = atoms[j].position[1] - atoms[subAtomIndex].position[1];
                        const dz = atoms[j].position[2] - atoms[subAtomIndex].position[2];
                        const dist = Math.sqrt(dx*dx + dy*dy + dz*dz);

                        if (dist < minDist) {
                            minDist = dist;
                            closestCatalytic = j;
                        }
                    }
                }

                // 如果找到了催化残基且距离合适，添加相互作用
                if (closestCatalytic >= 0 && minDist < 6) {
                    bonds.push({
                        start: subAtomIndex,
                        end: closestCatalytic,
                        color: 0x00ffff,
                        dashed: true
                    });
                }
            }

            return { atoms, bonds };
        },
        animation: {
            type: "catalysis",
            speed: 0.01,
            bondBreakIndex: 3
        }
    },

    // 葡萄糖 (C6H12O6)
    glucose: {
        name: "葡萄糖",
        formula: "C₆H₁₂O₆",
        description: "葡萄糖是最重要的单糖，是细胞能量代谢的主要燃料，也是许多复杂碳水化合物的基本组成单位。",
        atoms: [
            // 碳环骨架 (6个碳原子)
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, 0.8, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, 0, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, -1.5, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, -2.3, 0], color: 0x808080 },
            { symbol: "C", position: [0, -1.5, 0], color: 0x808080 },

            // 羟基 (OH)
            { symbol: "O", position: [-1.0, 0.8, 0], color: 0xff0000 },
            { symbol: "H", position: [-1.8, 0.3, 0], color: 0xffffff },

            { symbol: "O", position: [1.2, 2.2, 0], color: 0xff0000 },
            { symbol: "H", position: [2.0, 2.7, 0], color: 0xffffff },

            { symbol: "O", position: [3.6, 0.8, 0], color: 0xff0000 },
            { symbol: "H", position: [4.4, 0.3, 0], color: 0xffffff },

            { symbol: "O", position: [3.6, -2.3, 0], color: 0xff0000 },
            { symbol: "H", position: [3.6, -3.2, 0], color: 0xffffff },

            { symbol: "O", position: [1.2, -3.7, 0], color: 0xff0000 },
            { symbol: "H", position: [0.4, -4.2, 0], color: 0xffffff },

            // 氢原子 (简化，只显示部分)
            { symbol: "H", position: [0, 0, 1.0], color: 0xffffff },
            { symbol: "H", position: [1.2, 0.8, 1.0], color: 0xffffff },
            { symbol: "H", position: [2.4, 0, 1.0], color: 0xffffff },
            { symbol: "H", position: [2.4, -1.5, 1.0], color: 0xffffff },
            { symbol: "H", position: [1.2, -2.3, 1.0], color: 0xffffff },
            { symbol: "H", position: [0, -1.5, 1.0], color: 0xffffff }
        ],
        bonds: [
            // 碳环骨架
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 5 },
            { start: 5, end: 0 },

            // 羟基连接
            { start: 0, end: 6 },
            { start: 6, end: 7 },

            { start: 1, end: 8 },
            { start: 8, end: 9 },

            { start: 2, end: 10 },
            { start: 10, end: 11 },

            { start: 3, end: 12 },
            { start: 12, end: 13 },

            { start: 4, end: 14 },
            { start: 14, end: 15 },

            // 氢原子连接
            { start: 0, end: 16 },
            { start: 1, end: 17 },
            { start: 2, end: 18 },
            { start: 3, end: 19 },
            { start: 4, end: 20 },
            { start: 5, end: 21 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 1, 0],
            speed: 0.01
        }
    },

    // 阿司匹林 (C9H8O4)
    aspirin: {
        name: "阿司匹林",
        formula: "C₉H₈O₄",
        description: "阿司匹林是一种常用的解热镇痛药，也具有抗炎和抗血小板聚集的作用。",
        atoms: [
            // 苯环
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "C", position: [0, 1.4, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, 2.1, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, 1.4, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, 0, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, -0.7, 0], color: 0x808080 },

            // 羧基
            { symbol: "C", position: [3.6, -0.7, 0], color: 0x808080 },
            { symbol: "O", position: [3.6, -2.1, 0], color: 0xff0000 },
            { symbol: "O", position: [4.8, -0.1, 0], color: 0xff0000 },

            // 乙酰基
            { symbol: "O", position: [1.2, 3.5, 0], color: 0xff0000 },
            { symbol: "C", position: [2.4, 4.2, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, 5.7, 0], color: 0x808080 },
            { symbol: "O", position: [3.6, 3.6, 0], color: 0xff0000 },

            // 氢原子 (简化，只显示部分)
            { symbol: "H", position: [-0.9, -0.5, 0], color: 0xffffff },
            { symbol: "H", position: [-0.9, 1.9, 0], color: 0xffffff },
            { symbol: "H", position: [3.3, 1.9, 0], color: 0xffffff },
            { symbol: "H", position: [1.2, -1.8, 0], color: 0xffffff },
            { symbol: "H", position: [4.8, -2.6, 0], color: 0xffffff },
            { symbol: "H", position: [1.5, 6.2, 0], color: 0xffffff },
            { symbol: "H", position: [3.3, 6.2, 0], color: 0xffffff },
            { symbol: "H", position: [2.4, 6.0, 1.0], color: 0xffffff }
        ],
        bonds: [
            // 苯环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 5 },
            { start: 5, end: 0 },

            // 羧基
            { start: 4, end: 6 },
            { start: 6, end: 7 },
            { start: 6, end: 8, type: "double" },

            // 乙酰基
            { start: 2, end: 9 },
            { start: 9, end: 10 },
            { start: 10, end: 11 },
            { start: 10, end: 12, type: "double" },

            // 氢原子
            { start: 0, end: 13 },
            { start: 1, end: 14 },
            { start: 3, end: 15 },
            { start: 5, end: 16 },
            { start: 7, end: 17 },
            { start: 11, end: 18 },
            { start: 11, end: 19 },
            { start: 11, end: 20 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 1, 0],
            speed: 0.008
        }
    },

    // 氯化钠晶体 (NaCl)
    sodiumChloride: {
        name: "氯化钠晶体",
        formula: "NaCl",
        description: "氯化钠是食盐的主要成分，形成立方晶格结构，是离子晶体的典型代表。",
        // 动态生成晶体结构
        generateStructure: function(size = 2) {
            const atoms = [];
            const bonds = [];
            const latticeConstant = 2.8; // 晶格常数

            // 生成NaCl晶体结构 (面心立方)
            for (let x = 0; x < size; x++) {
                for (let y = 0; y < size; y++) {
                    for (let z = 0; z < size; z++) {
                        const xPos = x * latticeConstant;
                        const yPos = y * latticeConstant;
                        const zPos = z * latticeConstant;

                        // 在晶格点放置Na+离子
                        atoms.push({
                            symbol: "Na",
                            position: [xPos, yPos, zPos],
                            color: 0x0055ff // 蓝色表示钠离子
                        });

                        // 在面心位置放置Cl-离子
                        if (x < size - 1 && y < size - 1) {
                            atoms.push({
                                symbol: "Cl",
                                position: [xPos + latticeConstant/2, yPos + latticeConstant/2, zPos],
                                color: 0x00ff00 // 绿色表示氯离子
                            });
                        }

                        if (x < size - 1 && z < size - 1) {
                            atoms.push({
                                symbol: "Cl",
                                position: [xPos + latticeConstant/2, yPos, zPos + latticeConstant/2],
                                color: 0x00ff00
                            });
                        }

                        if (y < size - 1 && z < size - 1) {
                            atoms.push({
                                symbol: "Cl",
                                position: [xPos, yPos + latticeConstant/2, zPos + latticeConstant/2],
                                color: 0x00ff00
                            });
                        }
                    }
                }
            }

            // 添加离子键 (简化，只显示最近邻)
            for (let i = 0; i < atoms.length; i++) {
                const atom1 = atoms[i];

                for (let j = i + 1; j < atoms.length; j++) {
                    const atom2 = atoms[j];

                    // 计算两个原子之间的距离
                    const dx = atom1.position[0] - atom2.position[0];
                    const dy = atom1.position[1] - atom2.position[1];
                    const dz = atom1.position[2] - atom2.position[2];
                    const distance = Math.sqrt(dx*dx + dy*dy + dz*dz);

                    // 如果是Na和Cl，且距离接近晶格常数的一半，则添加键
                    if (
                        ((atom1.symbol === "Na" && atom2.symbol === "Cl") ||
                         (atom1.symbol === "Cl" && atom2.symbol === "Na")) &&
                        Math.abs(distance - latticeConstant/2) < 0.1
                    ) {
                        bonds.push({
                            start: i,
                            end: j,
                            color: 0xcccccc,
                            dashed: true // 使用虚线表示离子键
                        });
                    }
                }
            }

            return { atoms, bonds };
        },
        animation: {
            type: "crystalVibration",
            frequency: 0.03,
            amplitude: 0.05
        }
    },

    // 冠状病毒 (简化模型)
    coronavirus: {
        name: "冠状病毒",
        formula: "病毒结构",
        description: "冠状病毒是一类有包膜的RNA病毒，其表面的刺突蛋白形成冠状结构，是导致COVID-19的病原体。",
        // 动态生成病毒结构
        generateStructure: function() {
            const atoms = [];
            const bonds = [];

            // 病毒核心 (简化为一个球体)
            const coreRadius = 10;
            const coreSegments = 10;

            // 生成核心表面的点
            for (let i = 0; i < coreSegments; i++) {
                const phi = Math.PI * i / (coreSegments - 1);
                const sinPhi = Math.sin(phi);
                const cosPhi = Math.cos(phi);

                for (let j = 0; j < coreSegments * 2; j++) {
                    const theta = 2 * Math.PI * j / (coreSegments * 2);
                    const sinTheta = Math.sin(theta);
                    const cosTheta = Math.cos(theta);

                    // 球面坐标转笛卡尔坐标
                    const x = coreRadius * sinPhi * cosTheta;
                    const y = coreRadius * sinPhi * sinTheta;
                    const z = coreRadius * cosPhi;

                    // 添加表面点
                    const atomIndex = atoms.length;
                    atoms.push({
                        symbol: "C",
                        position: [x, y, z],
                        color: 0x888888, // 灰色表示病毒核心
                        isCore: true
                    });

                    // 连接相邻点
                    if (j > 0) {
                        bonds.push({
                            start: atomIndex - 1,
                            end: atomIndex
                        });
                    }

                    // 连接到上一行
                    if (i > 0 && j % 2 === 0) {
                        const prevRowIndex = atomIndex - coreSegments * 2;
                        if (prevRowIndex >= 0) {
                            bonds.push({
                                start: prevRowIndex,
                                end: atomIndex
                            });
                        }
                    }

                    // 添加刺突蛋白 (在部分表面点)
                    if ((i % 3 === 1 && j % 4 === 0) || (i % 3 === 2 && j % 4 === 2)) {
                        // 刺突基部
                        const spikeBaseIndex = atoms.length;
                        atoms.push({
                            symbol: "N",
                            position: [x * 1.1, y * 1.1, z * 1.1],
                            color: 0xff5500, // 橙色表示刺突蛋白
                            isSpike: true
                        });

                        // 连接刺突基部到表面
                        bonds.push({
                            start: atomIndex,
                            end: spikeBaseIndex
                        });

                        // 刺突中部
                        const spikeMidIndex = atoms.length;
                        atoms.push({
                            symbol: "O",
                            position: [x * 1.3, y * 1.3, z * 1.3],
                            color: 0xff5500,
                            isSpike: true
                        });

                        // 连接刺突中部到基部
                        bonds.push({
                            start: spikeBaseIndex,
                            end: spikeMidIndex
                        });

                        // 刺突顶部 (冠状结构)
                        const spikeTopIndex = atoms.length;
                        atoms.push({
                            symbol: "S",
                            position: [x * 1.5, y * 1.5, z * 1.5],
                            color: 0xff0000, // 红色表示刺突顶部
                            isSpike: true
                        });

                        // 连接刺突顶部到中部
                        bonds.push({
                            start: spikeMidIndex,
                            end: spikeTopIndex
                        });
                    }
                }

                // 闭合每一行
                if (atoms.length > 0 && i > 0) {
                    const rowStart = atoms.length - coreSegments * 2;
                    const rowEnd = atoms.length - 1;
                    bonds.push({
                        start: rowStart,
                        end: rowEnd
                    });
                }
            }

            return { atoms, bonds };
        },
        animation: {
            type: "virusFloat",
            speed: 0.005,
            amplitude: 0.3
        }
    },

    // 噬菌体 (简化模型)
    bacteriophage: {
        name: "噬菌体",
        formula: "病毒结构",
        description: "噬菌体是感染细菌的病毒，具有独特的头部和尾部结构，是分子生物学研究中的重要工具。",
        // 动态生成噬菌体结构
        generateStructure: function() {
            const atoms = [];
            const bonds = [];

            // 头部 (二十面体)
            const headRadius = 8;
            const headCenter = [0, 15, 0];

            // 创建二十面体顶点
            const phi = (1 + Math.sqrt(5)) / 2; // 黄金比例
            const vertices = [
                [-1, phi, 0], [1, phi, 0], [-1, -phi, 0], [1, -phi, 0],
                [0, -1, phi], [0, 1, phi], [0, -1, -phi], [0, 1, -phi],
                [phi, 0, -1], [phi, 0, 1], [-phi, 0, -1], [-phi, 0, 1]
            ];

            // 缩放并移动顶点
            const headVertices = vertices.map(v => [
                v[0] * headRadius + headCenter[0],
                v[1] * headRadius + headCenter[1],
                v[2] * headRadius + headCenter[2]
            ]);

            // 添加头部顶点
            for (let i = 0; i < headVertices.length; i++) {
                atoms.push({
                    symbol: "P",
                    position: headVertices[i],
                    color: 0x0088ff, // 蓝色表示头部
                    isHead: true
                });
            }

            // 连接头部顶点 (二十面体的边)
            const headEdges = [
                [0, 1], [0, 5], [0, 7], [0, 10], [0, 11],
                [1, 5], [1, 7], [1, 8], [1, 9],
                [2, 3], [2, 4], [2, 6], [2, 10], [2, 11],
                [3, 4], [3, 6], [3, 8], [3, 9],
                [4, 9], [4, 11], [5, 9], [5, 11],
                [6, 8], [6, 10], [7, 8], [7, 10]
            ];

            for (const [start, end] of headEdges) {
                bonds.push({
                    start: start,
                    end: end
                });
            }

            // 添加头部内部的DNA (简化)
            const dnaStartIndex = atoms.length;
            const dnaPoints = 20;
            const dnaRadius = 4;

            for (let i = 0; i < dnaPoints; i++) {
                const angle = (i / dnaPoints) * Math.PI * 4;
                const height = (i / dnaPoints) * headRadius * 1.2;

                const x = headCenter[0] + dnaRadius * Math.cos(angle) * (1 - height / (headRadius * 1.5));
                const y = headCenter[1] - height + headRadius * 0.5;
                const z = headCenter[2] + dnaRadius * Math.sin(angle) * (1 - height / (headRadius * 1.5));

                atoms.push({
                    symbol: i % 2 === 0 ? "N" : "O",
                    position: [x, y, z],
                    color: i % 2 === 0 ? 0x0000ff : 0xff0000,
                    isDNA: true
                });

                // 连接DNA链
                if (i > 0) {
                    bonds.push({
                        start: dnaStartIndex + i - 1,
                        end: dnaStartIndex + i,
                        color: 0xffff00
                    });
                }
            }

            // 尾部结构
            const tailStartIndex = atoms.length;
            const tailLength = 25;
            const tailWidth = 1.5;

            // 尾部主干
            for (let i = 0; i < tailLength; i++) {
                const y = headCenter[1] - headRadius - i * 1.2;

                atoms.push({
                    symbol: "C",
                    position: [0, y, 0],
                    color: 0x888888,
                    isTail: true
                });

                // 连接尾部节点
                if (i > 0) {
                    bonds.push({
                        start: tailStartIndex + i - 1,
                        end: tailStartIndex + i
                    });
                }

                // 添加尾部纤维 (每隔几个节点)
                if (i > 5 && i < tailLength - 5 && i % 4 === 0) {
                    const fiberStartIndex = atoms.length;
                    const fiberLength = 6;
                    const fiberAngles = [0, Math.PI/2, Math.PI, Math.PI*3/2];

                    for (const angle of fiberAngles) {
                        const fiberNodes = [];

                        for (let j = 0; j < fiberLength; j++) {
                            const distance = (j + 1) * 1.0;
                            const x = distance * Math.cos(angle);
                            const z = distance * Math.sin(angle);

                            const fiberNodeIndex = atoms.length;
                            atoms.push({
                                symbol: "N",
                                position: [x, y, z],
                                color: 0x00aa00,
                                isFiber: true
                            });

                            fiberNodes.push(fiberNodeIndex);

                            // 连接纤维节点
                            if (j > 0) {
                                bonds.push({
                                    start: fiberNodes[j-1],
                                    end: fiberNodes[j]
                                });
                            }
                        }

                        // 连接纤维到尾部主干
                        bonds.push({
                            start: tailStartIndex + i,
                            end: fiberNodes[0]
                        });
                    }
                }
            }

            // 连接头部到尾部
            bonds.push({
                start: headVertices.length - 1,
                end: tailStartIndex
            });

            // 尾部尖刺 (用于穿透细菌)
            const spikeStartIndex = atoms.length;
            const spikeLength = 6;

            for (let i = 0; i < spikeLength; i++) {
                const y = headCenter[1] - headRadius - tailLength * 1.2 - i * 0.8;
                const width = tailWidth * (1 - i / spikeLength);

                // 中心尖刺
                atoms.push({
                    symbol: "S",
                    position: [0, y, 0],
                    color: 0xaa0000,
                    isSpike: true
                });

                // 连接尖刺节点
                if (i > 0) {
                    bonds.push({
                        start: spikeStartIndex + i - 1,
                        end: spikeStartIndex + i
                    });
                } else {
                    // 连接尖刺到尾部
                    bonds.push({
                        start: tailStartIndex + tailLength - 1,
                        end: spikeStartIndex
                    });
                }
            }

            return { atoms, bonds };
        },
        animation: {
            type: "phageAttack",
            speed: 0.01,
            amplitude: 0.2
        }
    }
};

// 为动态生成的分子结构添加数据
(function() {
    // 生成DNA结构
    const dnaStructure = MOLECULES.dna.generateStructure();
    MOLECULES.dna.atoms = dnaStructure.atoms;
    MOLECULES.dna.bonds = dnaStructure.bonds;

    // 生成蛋白质结构
    const proteinStructure = MOLECULES.protein.generateStructure();
    MOLECULES.protein.atoms = proteinStructure.atoms;
    MOLECULES.protein.bonds = proteinStructure.bonds;

    // 生成RNA结构
    const rnaStructure = MOLECULES.rna.generateStructure();
    MOLECULES.rna.atoms = rnaStructure.atoms;
    MOLECULES.rna.bonds = rnaStructure.bonds;

    // 生成磷脂双分子层
    const lipidStructure = MOLECULES.phospholipidBilayer.generateStructure();
    MOLECULES.phospholipidBilayer.atoms = lipidStructure.atoms;
    MOLECULES.phospholipidBilayer.bonds = lipidStructure.bonds;

    // 生成β折叠结构
    const betaSheetStructure = MOLECULES.betaSheet.generateStructure();
    MOLECULES.betaSheet.atoms = betaSheetStructure.atoms;
    MOLECULES.betaSheet.bonds = betaSheetStructure.bonds;

    // 生成酶-底物复合物
    const enzymeStructure = MOLECULES.enzymeSubstrate.generateStructure();
    MOLECULES.enzymeSubstrate.atoms = enzymeStructure.atoms;
    MOLECULES.enzymeSubstrate.bonds = enzymeStructure.bonds;

    // 生成氯化钠晶体结构
    const nacStructure = MOLECULES.sodiumChloride.generateStructure();
    MOLECULES.sodiumChloride.atoms = nacStructure.atoms;
    MOLECULES.sodiumChloride.bonds = nacStructure.bonds;

    // 生成冠状病毒结构
    const covidStructure = MOLECULES.coronavirus.generateStructure();
    MOLECULES.coronavirus.atoms = covidStructure.atoms;
    MOLECULES.coronavirus.bonds = covidStructure.bonds;

    // 生成噬菌体结构
    const phageStructure = MOLECULES.bacteriophage.generateStructure();
    MOLECULES.bacteriophage.atoms = phageStructure.atoms;
    MOLECULES.bacteriophage.bonds = phageStructure.bonds;
})();

// 添加扩展分子
(function() {
    // 色氨酸 (Tryptophan)
    MOLECULES.tryptophan = {
        name: "色氨酸",
        formula: "C₁₁H₁₂N₂O₂",
        description: "色氨酸是一种含有吲哚环的必需氨基酸，是蛋白质合成的重要组成部分，也是血清素的前体。",
        atoms: [
            // 主链部分
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1.2, 0.5, 0], color: 0x808080 }, // α-碳
            { symbol: "C", position: [2.2, -0.5, 0], color: 0x808080 }, // 羧基碳
            { symbol: "O", position: [3.4, -0.2, 0], color: 0xff0000 }, // 羧基氧
            { symbol: "O", position: [2.0, -1.8, 0], color: 0xff0000 }, // 羧基氧

            // 侧链部分 - 吲哚环
            { symbol: "C", position: [1.2, 2.0, 0], color: 0x808080 }, // β-碳
            { symbol: "C", position: [2.4, 2.8, 0], color: 0x808080 }, // 连接吲哚环的碳

            // 吲哚环 - 六元环
            { symbol: "C", position: [2.4, 4.2, 0], color: 0x808080 },
            { symbol: "C", position: [3.6, 5.0, 0], color: 0x808080 },
            { symbol: "C", position: [4.8, 4.2, 0], color: 0x808080 },
            { symbol: "C", position: [4.8, 2.8, 0], color: 0x808080 },
            { symbol: "C", position: [3.6, 2.0, 0], color: 0x808080 },

            // 吲哚环 - 五元环
            { symbol: "N", position: [3.6, 0.6, 0], color: 0x0000ff },
            { symbol: "C", position: [4.8, 0.0, 0], color: 0x808080 },
            { symbol: "C", position: [6.0, 0.8, 0], color: 0x808080 },
            { symbol: "C", position: [6.0, 2.2, 0], color: 0x808080 },

            // 氢原子 (简化，只添加关键的氢)
            { symbol: "H", position: [-0.8, 0.5, 0], color: 0xffffff }, // 氨基氢
            { symbol: "H", position: [0.0, -1.0, 0], color: 0xffffff }, // 氨基氢
            { symbol: "H", position: [1.2, 0.5, 1.0], color: 0xffffff }, // α-碳氢
            { symbol: "H", position: [2.8, -0.2, 1.0], color: 0xffffff }, // 吲哚环氮氢
        ],
        bonds: [
            // 主链键
            { start: 0, end: 1 }, // N-Cα
            { start: 1, end: 2 }, // Cα-C
            { start: 2, end: 3, type: "double" }, // C=O
            { start: 2, end: 4 }, // C-O

            // 侧链键
            { start: 1, end: 5 }, // Cα-Cβ
            { start: 5, end: 6 }, // Cβ-C

            // 吲哚环 - 六元环
            { start: 6, end: 7 },
            { start: 7, end: 8 },
            { start: 8, end: 9 },
            { start: 9, end: 10 },
            { start: 10, end: 11 },
            { start: 11, end: 6 },

            // 吲哚环 - 五元环
            { start: 11, end: 12 },
            { start: 12, end: 13 },
            { start: 13, end: 14 },
            { start: 14, end: 15 },
            { start: 15, end: 10 },

            // 氢键
            { start: 0, end: 16 },
            { start: 0, end: 17 },
            { start: 1, end: 18 },
            { start: 12, end: 19 }
        ],
        animation: {
            type: "molecularDynamics",
            frequency: 0.03,
            amplitude: 0.12,
            speed: 1.0
        }
    };

    // 组氨酸 (Histidine)
    MOLECULES.histidine = {
        name: "组氨酸",
        formula: "C₆H₉N₃O₂",
        description: "组氨酸是一种含有咪唑环的氨基酸，在蛋白质中常作为催化位点，参与多种酶的活性中心。",
        atoms: [
            // 主链部分
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1.2, 0.5, 0], color: 0x808080 }, // α-碳
            { symbol: "C", position: [2.2, -0.5, 0], color: 0x808080 }, // 羧基碳
            { symbol: "O", position: [3.4, -0.2, 0], color: 0xff0000 }, // 羧基氧
            { symbol: "O", position: [2.0, -1.8, 0], color: 0xff0000 }, // 羧基氧

            // 侧链部分 - 咪唑环
            { symbol: "C", position: [1.2, 2.0, 0], color: 0x808080 }, // β-碳
            { symbol: "C", position: [2.4, 2.8, 0], color: 0x808080 }, // 连接咪唑环的碳

            // 咪唑环
            { symbol: "N", position: [2.4, 4.2, 0], color: 0x0000ff },
            { symbol: "C", position: [3.6, 4.8, 0], color: 0x808080 },
            { symbol: "N", position: [4.6, 4.0, 0], color: 0x0000ff },
            { symbol: "C", position: [4.0, 2.8, 0], color: 0x808080 },

            // 氢原子 (简化)
            { symbol: "H", position: [-0.8, 0.5, 0], color: 0xffffff }, // 氨基氢
            { symbol: "H", position: [0.0, -1.0, 0], color: 0xffffff }, // 氨基氢
            { symbol: "H", position: [1.2, 0.5, 1.0], color: 0xffffff }, // α-碳氢
            { symbol: "H", position: [1.6, 4.8, 0], color: 0xffffff }, // 咪唑环氮氢
        ],
        bonds: [
            // 主链键
            { start: 0, end: 1 }, // N-Cα
            { start: 1, end: 2 }, // Cα-C
            { start: 2, end: 3, type: "double" }, // C=O
            { start: 2, end: 4 }, // C-O

            // 侧链键
            { start: 1, end: 5 }, // Cα-Cβ
            { start: 5, end: 6 }, // Cβ-C

            // 咪唑环
            { start: 6, end: 7 },
            { start: 7, end: 8 },
            { start: 8, end: 9 },
            { start: 9, end: 10 },
            { start: 10, end: 6 },

            // 氢键
            { start: 0, end: 11 },
            { start: 0, end: 12 },
            { start: 1, end: 13 },
            { start: 7, end: 14 }
        ],
        animation: {
            type: "molecularDynamics",
            frequency: 0.03,
            amplitude: 0.12,
            speed: 1.0
        }
    };

    // 腺嘌呤 (Adenine)
    MOLECULES.adenine = {
        name: "腺嘌呤",
        formula: "C₅H₅N₅",
        description: "腺嘌呤是DNA和RNA中的一种嘌呤碱基，与胸腺嘧啶(DNA)或尿嘧啶(RNA)形成碱基对。",
        atoms: [
            // 嘌呤环系统 - 五元环
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1.2, 0.5, 0], color: 0x808080 },
            { symbol: "N", position: [2.2, -0.3, 0], color: 0x0000ff },
            { symbol: "C", position: [1.6, -1.5, 0], color: 0x808080 },
            { symbol: "C", position: [0.2, -1.3, 0], color: 0x808080 },

            // 嘌呤环系统 - 六元环
            { symbol: "C", position: [2.4, 1.7, 0], color: 0x808080 },
            { symbol: "N", position: [3.7, 1.5, 0], color: 0x0000ff },
            { symbol: "C", position: [4.0, 0.2, 0], color: 0x808080 },
            { symbol: "N", position: [3.4, -0.8, 0], color: 0x0000ff },

            // 氨基
            { symbol: "N", position: [2.1, 3.0, 0], color: 0x0000ff },

            // 氢原子 (简化)
            { symbol: "H", position: [-0.8, 0.5, 0], color: 0xffffff },
            { symbol: "H", position: [2.0, -2.5, 0], color: 0xffffff },
            { symbol: "H", position: [-0.5, -2.0, 0], color: 0xffffff },
            { symbol: "H", position: [5.0, 0.0, 0], color: 0xffffff },
            { symbol: "H", position: [1.2, 3.5, 0], color: 0xffffff },
            { symbol: "H", position: [3.0, 3.5, 0], color: 0xffffff }
        ],
        bonds: [
            // 五元环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 0 },

            // 六元环
            { start: 1, end: 5 },
            { start: 5, end: 6 },
            { start: 6, end: 7 },
            { start: 7, end: 8 },
            { start: 8, end: 2 },

            // 氨基
            { start: 5, end: 9 },

            // 氢键
            { start: 0, end: 10 },
            { start: 3, end: 11 },
            { start: 4, end: 12 },
            { start: 7, end: 13 },
            { start: 9, end: 14 },
            { start: 9, end: 15 }
        ],
        animation: {
            type: "conformationalChange",
            amplitude: 0.5,
            targetMode: "twist",
            speed: 0.5
        }
    };

    // 胸腺嘧啶 (Thymine)
    MOLECULES.thymine = {
        name: "胸腺嘧啶",
        formula: "C₅H₆N₂O₂",
        description: "胸腺嘧啶是DNA中的一种嘧啶碱基，与腺嘌呤形成碱基对。在RNA中，它被尿嘧啶替代。",
        atoms: [
            // 嘧啶环
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "C", position: [1.2, 0.5, 0], color: 0x808080 },
            { symbol: "N", position: [2.4, -0.2, 0], color: 0x0000ff },
            { symbol: "C", position: [2.4, -1.6, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, -2.3, 0], color: 0x808080 },
            { symbol: "C", position: [0, -1.4, 0], color: 0x808080 },

            // 羰基
            { symbol: "O", position: [3.5, -2.2, 0], color: 0xff0000 },
            { symbol: "O", position: [1.2, -3.7, 0], color: 0xff0000 },

            // 甲基
            { symbol: "C", position: [1.2, 2.0, 0], color: 0x808080 },

            // 氢原子 (简化)
            { symbol: "H", position: [-0.8, 0.5, 0], color: 0xffffff },
            { symbol: "H", position: [3.2, 0.3, 0], color: 0xffffff },
            { symbol: "H", position: [-1.0, -1.8, 0], color: 0xffffff },
            { symbol: "H", position: [0.2, 2.5, 0], color: 0xffffff },
            { symbol: "H", position: [1.8, 2.5, 0.7], color: 0xffffff },
            { symbol: "H", position: [1.8, 2.5, -0.7], color: 0xffffff }
        ],
        bonds: [
            // 嘧啶环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 5 },
            { start: 5, end: 0 },

            // 羰基
            { start: 3, end: 6, type: "double" },
            { start: 4, end: 7, type: "double" },

            // 甲基
            { start: 1, end: 8 },

            // 氢键
            { start: 0, end: 9 },
            { start: 2, end: 10 },
            { start: 5, end: 11 },
            { start: 8, end: 12 },
            { start: 8, end: 13 },
            { start: 8, end: 14 }
        ],
        animation: {
            type: "conformationalChange",
            amplitude: 0.5,
            targetMode: "twist",
            speed: 0.5
        }
    };

    // 多巴胺 (Dopamine)
    MOLECULES.dopamine = {
        name: "多巴胺",
        formula: "C₈H₁₁NO₂",
        description: "多巴胺是一种重要的神经递质，参与大脑的奖励、动机和运动控制等功能。",
        atoms: [
            // 苯环
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, 0.7, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, 0, 0], color: 0x808080 },
            { symbol: "C", position: [2.4, -1.4, 0], color: 0x808080 },
            { symbol: "C", position: [1.2, -2.1, 0], color: 0x808080 },
            { symbol: "C", position: [0, -1.4, 0], color: 0x808080 },

            // 羟基
            { symbol: "O", position: [1.2, 2.1, 0], color: 0xff0000 },
            { symbol: "O", position: [3.6, 0.7, 0], color: 0xff0000 },

            // 乙胺侧链
            { symbol: "C", position: [-1.2, 0.7, 0], color: 0x808080 },
            { symbol: "C", position: [-2.4, 0, 0], color: 0x808080 },
            { symbol: "N", position: [-3.6, 0.7, 0], color: 0x0000ff },

            // 氢原子 (简化)
            { symbol: "H", position: [2.4, -2.2, 0.7], color: 0xffffff },
            { symbol: "H", position: [1.2, -3.2, 0], color: 0xffffff },
            { symbol: "H", position: [-0.8, -2.0, 0], color: 0xffffff },
            { symbol: "H", position: [2.1, 2.5, 0], color: 0xffffff },
            { symbol: "H", position: [4.2, 0.0, 0], color: 0xffffff }
        ],
        bonds: [
            // 苯环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4 },
            { start: 4, end: 5 },
            { start: 5, end: 0 },

            // 羟基
            { start: 1, end: 6 },
            { start: 2, end: 7 },

            // 乙胺侧链
            { start: 0, end: 8 },
            { start: 8, end: 9 },
            { start: 9, end: 10 },

            // 氢键
            { start: 3, end: 11 },
            { start: 4, end: 12 },
            { start: 5, end: 13 },
            { start: 6, end: 14 },
            { start: 7, end: 15 }
        ],
        animation: {
            type: "molecularDynamics",
            frequency: 0.05,
            amplitude: 0.15,
            speed: 1.2
        }
    };

    // 青霉素G (Penicillin G) - 简化版
    MOLECULES.penicillinG = {
        name: "青霉素G",
        formula: "C₁₆H₁₈N₂O₄S",
        description: "青霉素G是第一个被发现的抗生素，通过抑制细菌细胞壁的合成来杀死细菌。",
        atoms: [
            // β-内酰胺环
            { symbol: "C", position: [0, 0, 0], color: 0x808080 }, // C2
            { symbol: "C", position: [1.2, 0.7, 0], color: 0x808080 }, // C3
            { symbol: "N", position: [2.4, 0, 0], color: 0x0000ff }, // N4
            { symbol: "C", position: [2.4, -1.4, 0], color: 0x808080 }, // C5
            { symbol: "O", position: [3.6, -2.1, 0], color: 0xff0000 }, // O=C

            // 噻唑烷环
            { symbol: "C", position: [1.2, -2.1, 0], color: 0x808080 }, // C6
            { symbol: "S", position: [1.2, -3.5, 0.7], color: 0xffff00 }, // S1
            { symbol: "C", position: [2.6, -3.5, 0], color: 0x808080 }, // C7

            // 羧基
            { symbol: "C", position: [0, -2.8, -1.0], color: 0x808080 }, // C8
            { symbol: "O", position: [0, -4.2, -1.0], color: 0xff0000 }, // O9
            { symbol: "O", position: [-1.0, -2.1, -1.7], color: 0xff0000 }, // O10

            // 苯基乙酰基侧链
            { symbol: "C", position: [0, 1.4, 0], color: 0x808080 }, // C11
            { symbol: "O", position: [-1.0, 2.1, 0.7], color: 0xff0000 }, // O12
            { symbol: "C", position: [1.2, 2.1, -0.7], color: 0x808080 }, // C13

            // 苯环
            { symbol: "C", position: [1.2, 3.5, -0.7], color: 0x808080 }, // C14
            { symbol: "C", position: [2.4, 4.2, -0.7], color: 0x808080 }, // C15
            { symbol: "C", position: [2.4, 5.6, -0.7], color: 0x808080 }, // C16
            { symbol: "C", position: [1.2, 6.3, -0.7], color: 0x808080 }, // C17
            { symbol: "C", position: [0, 5.6, -0.7], color: 0x808080 }, // C18
            { symbol: "C", position: [0, 4.2, -0.7], color: 0x808080 }  // C19
        ],
        bonds: [
            // β-内酰胺环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 3 },
            { start: 3, end: 4, type: "double" },
            { start: 3, end: 5 },
            { start: 5, end: 0 },

            // 噻唑烷环
            { start: 5, end: 6 },
            { start: 6, end: 7 },
            { start: 7, end: 3 },

            // 羧基
            { start: 5, end: 8 },
            { start: 8, end: 9, type: "double" },
            { start: 8, end: 10 },

            // 苯基乙酰基侧链
            { start: 0, end: 11 },
            { start: 11, end: 12, type: "double" },
            { start: 11, end: 13 },

            // 苯环
            { start: 13, end: 14 },
            { start: 14, end: 15 },
            { start: 15, end: 16 },
            { start: 16, end: 17 },
            { start: 17, end: 18 },
            { start: 18, end: 14 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 1, 0],
            speed: 0.015
        }
    };

    // 输出调试信息
    console.log("扩展分子已添加到MOLECULES对象");
    console.log("新增分子:", ["tryptophan", "histidine", "adenine", "thymine", "dopamine", "penicillinG"].join(", "));
})();
