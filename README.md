# 分子生物学可视化项目

## 项目概述
这是一个基于Three.js的分子生物学可视化项目，可以展示各种分子的3D结构和动画效果。

## 最近修复的问题

### 1. 编码问题修复
- ✅ 修复了批处理文件的中文路径编码问题
- ✅ 添加了UTF-8编码支持 (`chcp 65001`)
- ✅ 清理了debug.log中的乱码内容

### 2. Three.js库优化
- ✅ 优化了Three.js加载策略，优先使用CDN
- ✅ 添加了多个CDN备用源，提高加载成功率
- ✅ 改进了错误处理和用户提示

### 3. WebGL兼容性改进
- ✅ 添加了WebGL支持检测
- ✅ 改进了渲染器初始化错误处理
- ✅ 提供了更好的错误提示信息

### 4. 用户体验优化
- ✅ 添加了favicon图标，解决404错误
- ✅ 改进了加载进度显示
- ✅ 优化了错误信息展示

## 使用方法

### 方法1：使用批处理文件（推荐）
1. 双击 `打开分子可视化程序.bat`
2. 等待Python服务器启动
3. 浏览器会自动打开项目页面

### 方法2：手动启动
1. 在项目目录打开命令行
2. 运行：`python -m http.server 8000`
3. 在浏览器中访问：`http://localhost:8000`

## 项目结构
```
分子生物学可视化/
├── index.html              # 主页面
├── css/
│   └── styles.css          # 样式文件
├── js/
│   ├── main.js            # 主程序入口
│   ├── MoleculeViewer.js  # 分子可视化器
│   ├── MoleculeLoader.js  # 分子数据加载器
│   ├── controls.js        # 控制面板
│   ├── FallbackRenderer.js # 备用渲染器
│   └── lib/               # 第三方库
├── data/
│   └── molecules*.js      # 分子数据文件
├── favicon.svg            # 网站图标
├── debug.log             # 调试日志
└── README.md             # 说明文档
```

## 技术特性
- 🔬 支持多种分子结构可视化
- 🎮 交互式3D控制（旋转、缩放、平移）
- 🎨 多种显示模式（球棍模型、空间填充等）
- 🎬 分子动画效果（振动、旋转等）
- 📱 响应式设计，支持移动设备
- 🔧 自动错误检测和恢复

## 浏览器兼容性
- ✅ Chrome (推荐)
- ✅ Firefox
- ✅ Safari
- ⚠️ Edge (需要启用WebGL)
- ❌ Internet Explorer (不支持)

## 故障排除

### 如果页面无法加载：
1. 检查网络连接
2. 尝试刷新页面
3. 检查浏览器是否支持WebGL
4. 查看浏览器控制台的错误信息

### 如果3D渲染失败：
1. 确保浏览器支持WebGL
2. 更新显卡驱动
3. 尝试使用其他浏览器
4. 检查硬件加速是否启用

## 开发说明
项目使用了以下主要技术：
- **Three.js**: 3D图形渲染
- **WebGL**: 硬件加速渲染
- **HTML5 Canvas**: 2D备用渲染
- **CSS3**: 现代样式和动画
- **JavaScript ES6+**: 现代JavaScript特性

## 更新日志
- **v1.4** (2025-01-27): 修复编码问题，优化Three.js加载，改进错误处理
- **v1.3** (之前): 基础功能实现