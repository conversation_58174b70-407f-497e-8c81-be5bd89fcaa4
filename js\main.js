/**
 * 主程序入口
 * 增强版 - 添加更多错误处理和调试信息
 */

// 全局错误处理函数
function showError(message) {
    console.error(message);

    // 移除任何现有的错误消息
    const existingErrors = document.querySelectorAll('.error-container');
    existingErrors.forEach(el => el.remove());

    // 在页面上显示错误信息
    const errorContainer = document.createElement('div');
    errorContainer.className = 'error-container';
    errorContainer.style.position = 'fixed';
    errorContainer.style.top = '50%';
    errorContainer.style.left = '50%';
    errorContainer.style.transform = 'translate(-50%, -50%)';
    errorContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    errorContainer.style.color = 'white';
    errorContainer.style.padding = '30px';
    errorContainer.style.borderRadius = '15px';
    errorContainer.style.zIndex = '1000';
    errorContainer.style.maxWidth = '80%';
    errorContainer.style.textAlign = 'center';
    errorContainer.style.boxShadow = '0 0 20px rgba(255, 0, 0, 0.3)';
    errorContainer.style.border = '1px solid #ff3333';

    const errorTitle = document.createElement('h3');
    errorTitle.textContent = '加载错误';
    errorTitle.style.color = '#ff5555';
    errorTitle.style.marginBottom = '15px';
    errorTitle.style.fontSize = '24px';

    const errorText = document.createElement('p');
    errorText.textContent = message;
    errorText.style.marginBottom = '20px';
    errorText.style.lineHeight = '1.5';
    errorText.style.fontSize = '16px';

    // 添加可能的解决方案
    const solutionsTitle = document.createElement('h4');
    solutionsTitle.textContent = '可能的解决方案:';
    solutionsTitle.style.color = '#3498db';
    solutionsTitle.style.marginBottom = '10px';

    const solutionsList = document.createElement('ul');
    solutionsList.style.textAlign = 'left';
    solutionsList.style.marginBottom = '20px';
    solutionsList.style.paddingLeft = '20px';

    const solutions = [
        '检查您的网络连接是否正常',
        '尝试使用Chrome或Firefox浏览器',
        '确保您的浏览器已启用JavaScript和WebGL',
        '清除浏览器缓存后重试'
    ];

    solutions.forEach(solution => {
        const li = document.createElement('li');
        li.textContent = solution;
        li.style.marginBottom = '5px';
        solutionsList.appendChild(li);
    });

    // 按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.justifyContent = 'center';
    buttonContainer.style.gap = '10px';
    buttonContainer.style.marginTop = '15px';

    // 刷新按钮
    const reloadButton = document.createElement('button');
    reloadButton.textContent = '刷新页面';
    reloadButton.style.padding = '10px 20px';
    reloadButton.style.backgroundColor = '#3498db';
    reloadButton.style.border = 'none';
    reloadButton.style.borderRadius = '5px';
    reloadButton.style.color = 'white';
    reloadButton.style.cursor = 'pointer';
    reloadButton.style.fontWeight = 'bold';
    reloadButton.style.transition = 'background-color 0.3s';
    reloadButton.onmouseover = function() {
        this.style.backgroundColor = '#2980b9';
    };
    reloadButton.onmouseout = function() {
        this.style.backgroundColor = '#3498db';
    };
    reloadButton.onclick = function() {
        window.location.reload();
    };

    // 重试按钮
    const retryButton = document.createElement('button');
    retryButton.textContent = '重试加载';
    retryButton.style.padding = '10px 20px';
    retryButton.style.backgroundColor = '#2ecc71';
    retryButton.style.border = 'none';
    retryButton.style.borderRadius = '5px';
    retryButton.style.color = 'white';
    retryButton.style.cursor = 'pointer';
    retryButton.style.fontWeight = 'bold';
    retryButton.style.transition = 'background-color 0.3s';
    retryButton.onmouseover = function() {
        this.style.backgroundColor = '#27ae60';
    };
    retryButton.onmouseout = function() {
        this.style.backgroundColor = '#2ecc71';
    };
    retryButton.onclick = function() {
        errorContainer.remove();
        initApp(0);
    };

    buttonContainer.appendChild(retryButton);
    buttonContainer.appendChild(reloadButton);

    // 组装错误对话框
    errorContainer.appendChild(errorTitle);
    errorContainer.appendChild(errorText);
    errorContainer.appendChild(solutionsTitle);
    errorContainer.appendChild(solutionsList);
    errorContainer.appendChild(buttonContainer);

    // 添加到页面
    document.body.appendChild(errorContainer);

    // 尝试显示基本的分子查看器界面
    const viewerContainer = document.getElementById('molecule-viewer');
    if (viewerContainer) {
        viewerContainer.style.backgroundColor = '#000';
        viewerContainer.innerHTML = `
            <div style="color: white; padding: 20px; text-align: center; display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
                <h3 style="color: #ff5555; margin-bottom: 15px;">分子查看器加载失败</h3>
                <p style="margin-bottom: 15px;">请检查控制台错误信息或尝试刷新页面</p>
                <div style="width: 50px; height: 50px; border: 5px solid #3498db; border-radius: 50%; border-top-color: transparent; animation: spin 1s linear infinite;"></div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
    }
}

// 检查THREE.js是否正确加载
function checkDependencies() {
    if (typeof THREE === 'undefined') {
        throw new Error('THREE.js库未加载，请检查网络连接或刷新页面');
    }

    if (typeof THREE.OrbitControls === 'undefined') {
        console.warn('OrbitControls未加载，将使用简化版替代');
        // 简化版的OrbitControls在index.html中已定义
    }

    if (typeof MoleculeLoader === 'undefined') {
        throw new Error('MoleculeLoader类未定义，请检查脚本加载顺序');
    }

    if (typeof MoleculeViewer === 'undefined') {
        throw new Error('MoleculeViewer类未定义，请检查脚本加载顺序');
    }

    if (typeof Controls === 'undefined') {
        throw new Error('Controls类未定义，请检查脚本加载顺序');
    }

    console.log('所有依赖项检查通过');
    return true;
}

// 初始化应用程序
function initApp(retryCount = 0) {
    console.log(`正在初始化应用程序 (尝试 ${retryCount + 1}/3)...`);

    try {
        // 检测浏览器类型
        const isEdge = navigator.userAgent.includes('Edg/');
        console.log('浏览器检测:', isEdge ? 'Microsoft Edge' : '其他浏览器');

        // 测试WebGL基本支持（不要求特定扩展）
        try {
            console.log('正在测试WebGL基本支持...');
            const testCanvas = document.createElement('canvas');
            const testRenderer = new THREE.WebGLRenderer({
                canvas: testCanvas,
                antialias: false, // 在Edge中禁用抗锯齿以提高兼容性
                alpha: true,
                preserveDrawingBuffer: true
            });

            const gl = testRenderer.getContext();
            if (!gl) {
                throw new Error('无法获取WebGL渲染上下文');
            }

            // 检查扩展但不要求必须存在
            if (isEdge) {
                const extensions = [
                    'OES_texture_float',
                    'OES_standard_derivatives'
                ];

                console.log('Edge浏览器WebGL扩展检查:');
                extensions.forEach(ext => {
                    const supported = gl.getExtension(ext);
                    console.log(`- ${ext}: ${supported ? '支持' : '不支持'}`);
                });

                console.log('注意: 即使某些扩展不支持，程序也会尝试使用兼容模式运行');
            }

            console.log('WebGL基本支持测试通过');
            testRenderer.dispose(); // 清理测试渲染器
        } catch (webglError) {
            console.error('WebGL基本支持测试失败:', webglError);

            // 只有在完全不支持WebGL时才抛出错误
            if (webglError.message.includes('无法获取WebGL渲染上下文')) {
                let errorMessage = 'WebGL完全不受支持: ' + webglError.message;
                if (isEdge) {
                    errorMessage += '\n\nEdge浏览器解决方案:\n';
                    errorMessage += '1. 确保已启用硬件加速\n';
                    errorMessage += '2. 在edge://flags中启用"WebGL"\n';
                    errorMessage += '3. 检查安全设置是否阻止了WebGL\n';
                    errorMessage += '4. 尝试使用Chrome或Firefox浏览器';
                }
                throw new Error(errorMessage);
            } else {
                // 其他错误只记录警告，继续执行
                console.warn('WebGL测试遇到问题，但将继续尝试初始化:', webglError);
            }
        }
        // 检查THREE是否已定义
        if (typeof THREE === 'undefined') {
            throw new Error('THREE未定义，请检查网络连接或刷新页面');
        }

        console.log('THREE版本:', THREE.REVISION);
        console.log('浏览器信息:', navigator.userAgent);

        // 检查其他依赖项
        try {
            checkDependencies();
        } catch (e) {
            console.error('依赖项检查失败:', e);

            // 尝试手动定义缺失的类
            if (typeof MoleculeViewer === 'undefined') {
                console.warn('MoleculeViewer未定义，创建备用类');
                window.MoleculeViewer = function(container) {
                    this.renderingFailed = true;
                    this.container = container;

                    // 显示错误信息
                    container.innerHTML = `
                        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;
                                    display: flex; flex-direction: column; align-items: center; justify-content: center;
                                    background-color: rgba(0,0,0,0.8); color: white; text-align: center; padding: 20px;">
                            <h3 style="color: #ff5555; margin-bottom: 15px;">加载错误</h3>
                            <p style="margin-bottom: 20px;">MoleculeViewer未定义，请检查相关脚本是否加载</p>
                            <button onclick="window.location.reload()"
                                    style="padding: 10px 20px; background-color: #3498db; border: none;
                                           border-radius: 5px; color: white; cursor: pointer; margin: 5px;">
                                刷新页面
                            </button>
                        </div>
                    `;

                    // 空方法
                    this.loadMolecule = function() {};
                    this.setDisplayMode = function() {};
                    this.setAnimationType = function() {};
                    this.setAnimationSpeed = function() {};
                    this.togglePlayPause = function() { return false; };
                    this.resetView = function() {};
                    this._onWindowResize = function() {};
                };
            }

            if (typeof MoleculeLoader === 'undefined') {
                console.warn('MoleculeLoader未定义，创建备用类');
                window.MoleculeLoader = function() {
                    this.molecules = {
                        test: {
                            name: "备用测试分子",
                            category: "测试",
                            atoms: [
                                { symbol: "C", position: [0, 0, 0] },
                                { symbol: "H", position: [0.5, 0.5, 0.5] }
                            ],
                            bonds: [
                                { atoms: [0, 1], order: 1 }
                            ]
                        }
                    };

                    this.getMolecule = function() { return this.molecules.test; };
                    this.getAvailableMolecules = function() { return ['test']; };
                    this.getMoleculeInfoHTML = function() { return '<p>分子信息不可用</p>'; };
                    this.loadPDB = function() { return Promise.reject(new Error('PDB加载不可用')); };
                };
            }

            if (typeof Controls === 'undefined') {
                console.warn('Controls未定义，创建备用类');
                window.Controls = function(viewer, loader) {
                    this.viewer = viewer;
                    this.loader = loader;

                    // 空方法
                    this._initControls = function() {};
                    this._populateMoleculeSelect = function() {};
                    this._loadDefaultMolecule = function() {};
                    this.updatePlayPauseButton = function() {};
                };
            }
        }

        // 检查WebGL支持
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) {
                throw new Error('WebGL不受支持');
            }
            console.log('WebGL支持检查通过');
        } catch (e) {
            throw new Error('WebGL不受支持，无法渲染3D内容: ' + e.message);
        }

        // 检查MOLECULES对象是否已加载
        if (typeof MOLECULES === 'undefined') {
            console.warn('MOLECULES对象未定义，将使用备用数据');
            // 创建基本的MOLECULES对象作为备份
            window.MOLECULES = {
                test: {
                    name: "测试分子",
                    category: "测试",
                    atoms: [
                        { symbol: "C", position: [0, 0, 0] },
                        { symbol: "H", position: [0.5, 0.5, 0.5] },
                        { symbol: "H", position: [-0.5, -0.5, 0.5] },
                        { symbol: "H", position: [0.5, -0.5, -0.5] },
                        { symbol: "H", position: [-0.5, 0.5, -0.5] }
                    ],
                    bonds: [
                        { atoms: [0, 1], order: 1 },
                        { atoms: [0, 2], order: 1 },
                        { atoms: [0, 3], order: 1 },
                        { atoms: [0, 4], order: 1 }
                    ]
                },
                water: {
                    name: "水分子",
                    category: "小分子",
                    atoms: [
                        { symbol: "O", position: [0, 0, 0] },
                        { symbol: "H", position: [0.8, 0.6, 0] },
                        { symbol: "H", position: [-0.8, 0.6, 0] }
                    ],
                    bonds: [
                        { atoms: [0, 1], order: 1 },
                        { atoms: [0, 2], order: 1 }
                    ]
                }
            };
        }

        // 初始化分子加载器
        console.log('正在初始化MoleculeLoader...');
        const moleculeLoader = new MoleculeLoader();
        console.log('MoleculeLoader初始化成功');

        // 初始化分子可视化器
        console.log('正在初始化MoleculeViewer...');
        const container = document.getElementById('molecule-viewer');

        if (!container) {
            throw new Error('找不到molecule-viewer容器元素');
        }
        console.log('容器元素尺寸:', container.clientWidth, 'x', container.clientHeight);

        const moleculeViewer = new MoleculeViewer(container);
        console.log('MoleculeViewer初始化成功');

        // 检查MoleculeViewer是否初始化失败
        if (moleculeViewer.renderingFailed) {
            console.warn('MoleculeViewer初始化失败，但将继续尝试初始化Controls');
        }

        // 初始化控制器
        console.log('正在初始化Controls...');
        const controls = new Controls(moleculeViewer, moleculeLoader);
        console.log('Controls初始化成功');

        // 添加窗口大小变化事件监听器
        window.addEventListener('resize', () => {
            console.log('窗口大小已变化');
            if (moleculeViewer && typeof moleculeViewer._onWindowResize === 'function') {
                moleculeViewer._onWindowResize();
            }
        });

        // 添加全局引用，方便调试
        window.app = {
            moleculeLoader,
            moleculeViewer,
            controls
        };

        console.log('分子生物学可视化程序已初始化');

        // 移除任何现有的错误消息
        const existingErrors = document.querySelectorAll('.error-container');
        existingErrors.forEach(el => el.remove());

    } catch (error) {
        console.error('初始化过程中发生错误:', error);

        // 如果还有重试次数，则延迟后重试
        if (retryCount < 2) {
            console.log(`将在2秒后重试初始化 (${retryCount + 1}/3)...`);
            setTimeout(() => {
                initApp(retryCount + 1);
            }, 2000);
        } else {
            // 已达到最大重试次数，显示错误
            showError(error.message || '初始化失败，请刷新页面重试');
        }
    }
}

// 主程序入口 - 由index.html中的脚本加载器调用
// initApp函数由index.html中的脚本加载器调用

// 确保showError函数在全局可用
window.showError = showError;
