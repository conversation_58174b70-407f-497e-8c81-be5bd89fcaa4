@echo off
:: Simple Molecular Visualization Launcher
:: Version: 1.3

title Molecular Visualization

echo === Debug Information === > debug.log
echo Current directory: %CD% >> debug.log
echo Date: %date% %time% >> debug.log

:: Verify files exist
if not exist "index.html" (
    echo Error: index.html not found >> debug.log
    echo ERROR: index.html file not found in current directory
    pause
    exit /b 1
)

:: Check Python
python --version >> debug.log 2>&1
if errorlevel 1 (
    echo Error: Python check failed >> debug.log
    echo ERROR: Python not found or not in PATH
    echo Please install Python from python.org
    pause
    exit /b 1
)

:: Start simple server
echo Starting server... >> debug.log
start "" python -m http.server 8000 --bind 127.0.0.1

:: Open browser after short delay
timeout /t 3 >nul
start "" "http://127.0.0.1:8000/index.html"

echo Server started at http://127.0.0.1:8000/index.html
echo Press any key to exit...
pause >nul
