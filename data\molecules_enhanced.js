/**
 * 分子数据定义文件
 * 增强版 - 包含更多种类的分子和更精细的结构
 * 大幅扩展分子种类，添加更多类型的分子和更精确的3D结构
 * 新增更多生物分子、药物分子和复杂结构
 */

// 使用全局变量定义分子数据
window.MOLECULES = {
    // ==================== 简单无机分子 ====================

    // 水分子 (H2O)
    water: {
        name: "水分子",
        formula: "H₂O",
        description: "水分子由一个氧原子和两个氢原子组成，是生命存在的基础。",
        atoms: [
            { symbol: "O", position: [0, 0, 0], color: 0xff0000 },
            { symbol: "H", position: [0.8, 0.6, 0], color: 0xffffff },
            { symbol: "H", position: [-0.8, 0.6, 0], color: 0xffffff }
        ],
        bonds: [
            { start: 0, end: 1 },
            { start: 0, end: 2 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.05,
            amplitude: 0.1
        }
    },

    // 二氧化碳 (CO2)
    carbonDioxide: {
        name: "二氧化碳",
        formula: "CO₂",
        description: "二氧化碳是一种重要的温室气体，由一个碳原子和两个氧原子组成，呈线性结构。",
        atoms: [
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "O", position: [1.2, 0, 0], color: 0xff0000 },
            { symbol: "O", position: [-1.2, 0, 0], color: 0xff0000 }
        ],
        bonds: [
            { start: 0, end: 1, type: "double" },
            { start: 0, end: 2, type: "double" }
        ],
        animation: {
            type: "vibration",
            frequency: 0.06,
            amplitude: 0.08
        }
    },

    // 氧气 (O2)
    oxygen: {
        name: "氧气",
        formula: "O₂",
        description: "氧气是地球大气的重要组成部分，由两个氧原子通过双键连接形成。",
        atoms: [
            { symbol: "O", position: [-0.6, 0, 0], color: 0xff0000 },
            { symbol: "O", position: [0.6, 0, 0], color: 0xff0000 }
        ],
        bonds: [
            { start: 0, end: 1, type: "double" }
        ],
        animation: {
            type: "vibration",
            frequency: 0.08,
            amplitude: 0.1
        }
    },

    // 氮气 (N2)
    nitrogen: {
        name: "氮气",
        formula: "N₂",
        description: "氮气是地球大气的主要成分，由两个氮原子通过三键连接形成。",
        atoms: [
            { symbol: "N", position: [-0.6, 0, 0], color: 0x0000ff },
            { symbol: "N", position: [0.6, 0, 0], color: 0x0000ff }
        ],
        bonds: [
            { start: 0, end: 1, type: "triple" }
        ],
        animation: {
            type: "vibration",
            frequency: 0.07,
            amplitude: 0.08
        }
    },

    // 氨 (NH3)
    ammonia: {
        name: "氨",
        formula: "NH₃",
        description: "氨是一种重要的化学品和肥料原料，分子呈三角锥形结构。",
        atoms: [
            { symbol: "N", position: [0, 0, 0], color: 0x0000ff },
            { symbol: "H", position: [0.8, 0.6, 0], color: 0xffffff },
            { symbol: "H", position: [-0.8, 0.6, 0], color: 0xffffff },
            { symbol: "H", position: [0, -0.8, 0.6], color: 0xffffff }
        ],
        bonds: [
            { start: 0, end: 1 },
            { start: 0, end: 2 },
            { start: 0, end: 3 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.06,
            amplitude: 0.12
        }
    },

    // 臭氧 (O3)
    ozone: {
        name: "臭氧",
        formula: "O₃",
        description: "臭氧是氧气的同素异形体，在平流层形成臭氧层，保护地球免受紫外线辐射。",
        atoms: [
            { symbol: "O", position: [0, 0, 0], color: 0xff0000 },
            { symbol: "O", position: [1.1, 0.6, 0], color: 0xff0000 },
            { symbol: "O", position: [-1.1, 0.6, 0], color: 0xff0000 }
        ],
        bonds: [
            { start: 0, end: 1 },
            { start: 0, end: 2 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.05,
            amplitude: 0.1
        }
    },

    // ==================== 简单有机分子 ====================

    // 甲烷 (CH4)
    methane: {
        name: "甲烷",
        formula: "CH₄",
        description: "甲烷是最简单的烷烃，由一个碳原子和四个氢原子组成，是天然气的主要成分。",
        atoms: [
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "H", position: [0.8, 0.8, 0.8], color: 0xffffff },
            { symbol: "H", position: [-0.8, -0.8, 0.8], color: 0xffffff },
            { symbol: "H", position: [0.8, -0.8, -0.8], color: 0xffffff },
            { symbol: "H", position: [-0.8, 0.8, -0.8], color: 0xffffff }
        ],
        bonds: [
            { start: 0, end: 1 },
            { start: 0, end: 2 },
            { start: 0, end: 3 },
            { start: 0, end: 4 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.04,
            amplitude: 0.12
        }
    },

    // 乙烷 (C2H6)
    ethane: {
        name: "乙烷",
        formula: "C₂H₆",
        description: "乙烷是第二简单的烷烃，由两个碳原子和六个氢原子组成。",
        atoms: [
            // 第一个碳原子及其氢原子
            { symbol: "C", position: [-0.7, 0, 0], color: 0x808080 },
            { symbol: "H", position: [-1.0, 0.9, 0.4], color: 0xffffff },
            { symbol: "H", position: [-1.0, -0.5, 0.9], color: 0xffffff },
            { symbol: "H", position: [-1.3, -0.4, -0.8], color: 0xffffff },

            // 第二个碳原子及其氢原子
            { symbol: "C", position: [0.7, 0, 0], color: 0x808080 },
            { symbol: "H", position: [1.0, 0.9, 0.4], color: 0xffffff },
            { symbol: "H", position: [1.0, -0.5, 0.9], color: 0xffffff },
            { symbol: "H", position: [1.3, -0.4, -0.8], color: 0xffffff }
        ],
        bonds: [
            // C-C键
            { start: 0, end: 4 },

            // 第一个碳的C-H键
            { start: 0, end: 1 },
            { start: 0, end: 2 },
            { start: 0, end: 3 },

            // 第二个碳的C-H键
            { start: 4, end: 5 },
            { start: 4, end: 6 },
            { start: 4, end: 7 }
        ],
        animation: {
            type: "rotation",
            axis: [1, 0, 0],
            speed: 0.02
        }
    },

    // 乙烯 (C2H4)
    ethene: {
        name: "乙烯",
        formula: "C₂H₄",
        description: "乙烯是最简单的烯烃，含有一个碳-碳双键，是重要的化工原料。",
        atoms: [
            // 第一个碳原子及其氢原子
            { symbol: "C", position: [-0.6, 0, 0], color: 0x808080 },
            { symbol: "H", position: [-1.2, 0.8, 0], color: 0xffffff },
            { symbol: "H", position: [-1.2, -0.8, 0], color: 0xffffff },

            // 第二个碳原子及其氢原子
            { symbol: "C", position: [0.6, 0, 0], color: 0x808080 },
            { symbol: "H", position: [1.2, 0.8, 0], color: 0xffffff },
            { symbol: "H", position: [1.2, -0.8, 0], color: 0xffffff }
        ],
        bonds: [
            // C=C双键
            { start: 0, end: 3, type: "double" },

            // 第一个碳的C-H键
            { start: 0, end: 1 },
            { start: 0, end: 2 },

            // 第二个碳的C-H键
            { start: 3, end: 4 },
            { start: 3, end: 5 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.05,
            amplitude: 0.1
        }
    },

    // 乙炔 (C2H2)
    acetylene: {
        name: "乙炔",
        formula: "C₂H₂",
        description: "乙炔是最简单的炔烃，含有一个碳-碳三键，常用于焊接。",
        atoms: [
            // 第一个碳原子及其氢原子
            { symbol: "C", position: [-0.6, 0, 0], color: 0x808080 },
            { symbol: "H", position: [-1.5, 0, 0], color: 0xffffff },

            // 第二个碳原子及其氢原子
            { symbol: "C", position: [0.6, 0, 0], color: 0x808080 },
            { symbol: "H", position: [1.5, 0, 0], color: 0xffffff }
        ],
        bonds: [
            // C≡C三键
            { start: 0, end: 2, type: "triple" },

            // C-H键
            { start: 0, end: 1 },
            { start: 2, end: 3 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.06,
            amplitude: 0.08
        }
    },

    // 丙烷 (C3H8)
    propane: {
        name: "丙烷",
        formula: "C₃H₈",
        description: "丙烷是一种常见的烷烃，是液化石油气的主要成分。",
        atoms: [
            // 第一个碳原子及其氢原子
            { symbol: "C", position: [-2.0, 0, 0], color: 0x808080 },
            { symbol: "H", position: [-2.0, 0.9, 0.4], color: 0xffffff },
            { symbol: "H", position: [-2.0, -0.5, 0.9], color: 0xffffff },
            { symbol: "H", position: [-2.8, -0.4, -0.5], color: 0xffffff },

            // 第二个碳原子及其氢原子
            { symbol: "C", position: [-0.7, 0, -0.5], color: 0x808080 },
            { symbol: "H", position: [-0.7, 0.9, -1.0], color: 0xffffff },
            { symbol: "H", position: [-0.7, -0.9, -1.0], color: 0xffffff },

            // 第三个碳原子及其氢原子
            { symbol: "C", position: [0.7, 0, 0], color: 0x808080 },
            { symbol: "H", position: [0.7, 0.9, 0.5], color: 0xffffff },
            { symbol: "H", position: [0.7, -0.9, 0.5], color: 0xffffff },
            { symbol: "H", position: [1.5, 0, -0.5], color: 0xffffff }
        ],
        bonds: [
            // C-C键
            { start: 0, end: 4 },
            { start: 4, end: 7 },

            // 第一个碳的C-H键
            { start: 0, end: 1 },
            { start: 0, end: 2 },
            { start: 0, end: 3 },

            // 第二个碳的C-H键
            { start: 4, end: 5 },
            { start: 4, end: 6 },

            // 第三个碳的C-H键
            { start: 7, end: 8 },
            { start: 7, end: 9 },
            { start: 7, end: 10 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 1, 0],
            speed: 0.015
        }
    },

    // 环丙烷 (C3H6)
    cyclopropane: {
        name: "环丙烷",
        formula: "C₃H₆",
        description: "环丙烷是最小的环烷烃，由三个碳原子形成一个三角形环。",
        atoms: [
            // 碳原子
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "C", position: [1.0, 0.6, 0], color: 0x808080 },
            { symbol: "C", position: [0.5, -1.0, 0], color: 0x808080 },

            // 氢原子
            { symbol: "H", position: [-0.5, 0.2, 0.9], color: 0xffffff },
            { symbol: "H", position: [-0.5, 0.2, -0.9], color: 0xffffff },
            { symbol: "H", position: [1.5, 0.8, 0.9], color: 0xffffff },
            { symbol: "H", position: [1.5, 0.8, -0.9], color: 0xffffff },
            { symbol: "H", position: [0.5, -1.5, 0.9], color: 0xffffff },
            { symbol: "H", position: [0.5, -1.5, -0.9], color: 0xffffff }
        ],
        bonds: [
            // C-C键形成环
            { start: 0, end: 1 },
            { start: 1, end: 2 },
            { start: 2, end: 0 },

            // C-H键
            { start: 0, end: 3 },
            { start: 0, end: 4 },
            { start: 1, end: 5 },
            { start: 1, end: 6 },
            { start: 2, end: 7 },
            { start: 2, end: 8 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 0, 1],
            speed: 0.02
        }
    },

    // 苯 (C6H6)
    benzene: {
        name: "苯",
        formula: "C₆H₆",
        description: "苯是最简单的芳香烃，具有六元环结构和离域π电子。",
        atoms: [
            // 碳原子环
            { symbol: "C", position: [0, 1.2, 0], color: 0x808080 },
            { symbol: "C", position: [1.04, 0.6, 0], color: 0x808080 },
            { symbol: "C", position: [1.04, -0.6, 0], color: 0x808080 },
            { symbol: "C", position: [0, -1.2, 0], color: 0x808080 },
            { symbol: "C", position: [-1.04, -0.6, 0], color: 0x808080 },
            { symbol: "C", position: [-1.04, 0.6, 0], color: 0x808080 },

            // 氢原子
            { symbol: "H", position: [0, 2.1, 0], color: 0xffffff },
            { symbol: "H", position: [1.82, 1.05, 0], color: 0xffffff },
            { symbol: "H", position: [1.82, -1.05, 0], color: 0xffffff },
            { symbol: "H", position: [0, -2.1, 0], color: 0xffffff },
            { symbol: "H", position: [-1.82, -1.05, 0], color: 0xffffff },
            { symbol: "H", position: [-1.82, 1.05, 0], color: 0xffffff }
        ],
        bonds: [
            // 碳环键 (交替单双键表示)
            { start: 0, end: 1, type: "aromatic" },
            { start: 1, end: 2, type: "aromatic" },
            { start: 2, end: 3, type: "aromatic" },
            { start: 3, end: 4, type: "aromatic" },
            { start: 4, end: 5, type: "aromatic" },
            { start: 5, end: 0, type: "aromatic" },

            // C-H键
            { start: 0, end: 6 },
            { start: 1, end: 7 },
            { start: 2, end: 8 },
            { start: 3, end: 9 },
            { start: 4, end: 10 },
            { start: 5, end: 11 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 0, 1],
            speed: 0.01
        }
    },

    // 乙醇 (C2H5OH)
    ethanol: {
        name: "乙醇",
        formula: "C₂H₅OH",
        description: "乙醇是一种常见的醇类，是酒精饮料中的主要成分，也被用作消毒剂。",
        atoms: [
            // 第一个碳原子及其氢原子
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "H", position: [0, 0.9, 0.4], color: 0xffffff },
            { symbol: "H", position: [0, -0.5, 0.9], color: 0xffffff },
            { symbol: "H", position: [-0.9, -0.3, -0.3], color: 0xffffff },

            // 第二个碳原子及其氢原子
            { symbol: "C", position: [1.2, 0, -0.5], color: 0x808080 },
            { symbol: "H", position: [1.2, 0.9, -1.0], color: 0xffffff },
            { symbol: "H", position: [1.2, -0.9, -1.0], color: 0xffffff },

            // 羟基
            { symbol: "O", position: [2.4, 0, 0.2], color: 0xff0000 },
            { symbol: "H", position: [3.1, 0, -0.3], color: 0xffffff }
        ],
        bonds: [
            // 第一个碳的键
            { start: 0, end: 1 },
            { start: 0, end: 2 },
            { start: 0, end: 3 },
            { start: 0, end: 4 },

            // 第二个碳的键
            { start: 4, end: 5 },
            { start: 4, end: 6 },
            { start: 4, end: 7 },

            // 羟基键
            { start: 7, end: 8 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 1, 0],
            speed: 0.01
        }
    },

    // 甲醛 (CH2O)
    formaldehyde: {
        name: "甲醛",
        formula: "CH₂O",
        description: "甲醛是最简单的醛类，是一种重要的工业原料，也是一种已知的致癌物。",
        atoms: [
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "O", position: [0, 1.2, 0], color: 0xff0000 },
            { symbol: "H", position: [-0.9, -0.5, 0], color: 0xffffff },
            { symbol: "H", position: [0.9, -0.5, 0], color: 0xffffff }
        ],
        bonds: [
            { start: 0, end: 1, type: "double" },
            { start: 0, end: 2 },
            { start: 0, end: 3 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.05,
            amplitude: 0.1
        }
    },

    // 丙酮 (C3H6O)
    acetone: {
        name: "丙酮",
        formula: "C₃H₆O",
        description: "丙酮是最简单的酮类，是一种常用的有机溶剂。",
        atoms: [
            // 中心碳原子和氧原子
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "O", position: [0, 1.2, 0], color: 0xff0000 },

            // 左侧甲基
            { symbol: "C", position: [-1.2, -0.5, 0], color: 0x808080 },
            { symbol: "H", position: [-1.2, -1.2, 0.8], color: 0xffffff },
            { symbol: "H", position: [-1.2, -1.0, -0.9], color: 0xffffff },
            { symbol: "H", position: [-2.1, 0.1, 0], color: 0xffffff },

            // 右侧甲基
            { symbol: "C", position: [1.2, -0.5, 0], color: 0x808080 },
            { symbol: "H", position: [1.2, -1.2, 0.8], color: 0xffffff },
            { symbol: "H", position: [1.2, -1.0, -0.9], color: 0xffffff },
            { symbol: "H", position: [2.1, 0.1, 0], color: 0xffffff }
        ],
        bonds: [
            // 中心碳与氧的双键
            { start: 0, end: 1, type: "double" },

            // 中心碳与两个甲基的连接
            { start: 0, end: 2 },
            { start: 0, end: 6 },

            // 左侧甲基的C-H键
            { start: 2, end: 3 },
            { start: 2, end: 4 },
            { start: 2, end: 5 },

            // 右侧甲基的C-H键
            { start: 6, end: 7 },
            { start: 6, end: 8 },
            { start: 6, end: 9 }
        ],
        animation: {
            type: "rotation",
            axis: [0, 1, 0],
            speed: 0.015
        }
    },

    // 乙酸 (CH3COOH)
    aceticAcid: {
        name: "乙酸",
        formula: "CH₃COOH",
        description: "乙酸是一种重要的羧酸，是醋的主要成分。",
        atoms: [
            // 甲基
            { symbol: "C", position: [-1.2, 0, 0], color: 0x808080 },
            { symbol: "H", position: [-1.5, 1.0, 0], color: 0xffffff },
            { symbol: "H", position: [-1.5, -0.5, 0.9], color: 0xffffff },
            { symbol: "H", position: [-1.8, -0.5, -0.9], color: 0xffffff },

            // 羧基
            { symbol: "C", position: [0, 0, 0], color: 0x808080 },
            { symbol: "O", position: [0.5, 1.1, 0], color: 0xff0000 },
            { symbol: "O", position: [0.8, -1.0, 0], color: 0xff0000 },
            { symbol: "H", position: [1.7, -0.7, 0], color: 0xffffff }
        ],
        bonds: [
            // 甲基C-H键
            { start: 0, end: 1 },
            { start: 0, end: 2 },
            { start: 0, end: 3 },

            // 甲基与羧基碳的连接
            { start: 0, end: 4 },

            // 羧基C=O双键
            { start: 4, end: 5, type: "double" },

            // 羧基C-O-H单键
            { start: 4, end: 6 },
            { start: 6, end: 7 }
        ],
        animation: {
            type: "vibration",
            frequency: 0.04,
            amplitude: 0.1
        }
    }
};

// 为动态生成的分子结构添加数据
(function() {
    // 这里可以添加动态生成的分子结构
})();
