/**
 * 化学反应引擎类
 * 负责处理分子间的化学反应和3D动画
 */
class ReactionEngine {
    constructor(viewer) {
        this.viewer = viewer;
        this.reactions = {};
        this.currentReaction = null;
        this.isReacting = false;
        this.reactionProgress = 0;
        this.reactionSpeed = 1.0;
        this.reactionParticles = [];

        // 初始化预定义反应
        this._initializeReactions();

        console.log('ReactionEngine初始化完成');
    }

    /**
     * 初始化预定义的化学反应
     * @private
     */
    _initializeReactions() {
        // 酸碱中和反应：HCl + NaOH → NaCl + H2O
        this.reactions.acidBase = {
            name: '酸碱中和反应',
            description: '氯化氢与氢氧化钠反应生成氯化钠和水',
            equation: 'HCl + NaOH → NaCl + H₂O',
            reactants: ['hydrogenChloride', 'sodiumHydroxide'],
            products: ['sodiumChloride', 'water'],
            type: 'acid-base',
            energyChange: -57.3, // kJ/mol
            duration: 3000, // 毫秒
            steps: [
                {
                    description: '反应物接近',
                    duration: 1000,
                    animation: 'approach'
                },
                {
                    description: '键断裂和形成',
                    duration: 1500,
                    animation: 'bondBreaking'
                },
                {
                    description: '产物分离',
                    duration: 500,
                    animation: 'separation'
                }
            ]
        };

        // 燃烧反应：CH4 + 2O2 → CO2 + 2H2O
        this.reactions.combustion = {
            name: '甲烷燃烧反应',
            description: '甲烷在氧气中燃烧生成二氧化碳和水',
            equation: 'CH₄ + 2O₂ → CO₂ + 2H₂O',
            reactants: ['methane', 'oxygen'],
            products: ['carbonDioxide', 'water'],
            type: 'combustion',
            energyChange: -890.3, // kJ/mol
            duration: 4000,
            steps: [
                {
                    description: '反应物混合',
                    duration: 1000,
                    animation: 'mixing'
                },
                {
                    description: '激活能突破',
                    duration: 1000,
                    animation: 'activation'
                },
                {
                    description: '键重排',
                    duration: 1500,
                    animation: 'rearrangement'
                },
                {
                    description: '产物形成',
                    duration: 500,
                    animation: 'formation'
                }
            ]
        };

        // 酯化反应：乙酸 + 乙醇 → 乙酸乙酯 + 水
        this.reactions.esterification = {
            name: '酯化反应',
            description: '乙酸与乙醇反应生成乙酸乙酯和水',
            equation: 'CH₃COOH + C₂H₅OH → CH₃COOC₂H₅ + H₂O',
            reactants: ['aceticAcid', 'ethanol'],
            products: ['ethylAcetate', 'water'],
            type: 'esterification',
            energyChange: -15.0, // kJ/mol
            duration: 5000,
            steps: [
                {
                    description: '分子定向',
                    duration: 1500,
                    animation: 'orientation'
                },
                {
                    description: '质子转移',
                    duration: 2000,
                    animation: 'protonTransfer'
                },
                {
                    description: '水分子消除',
                    duration: 1000,
                    animation: 'elimination'
                },
                {
                    description: '酯键形成',
                    duration: 500,
                    animation: 'esterBond'
                }
            ]
        };

        // 氨基酸肽键形成
        this.reactions.peptideBond = {
            name: '肽键形成反应',
            description: '两个氨基酸分子形成肽键',
            equation: 'AA₁ + AA₂ → Dipeptide + H₂O',
            reactants: ['glycine', 'alanine'],
            products: ['glycylAlanine', 'water'],
            type: 'condensation',
            energyChange: 17.0, // kJ/mol
            duration: 4500,
            steps: [
                {
                    description: '氨基酸接近',
                    duration: 1500,
                    animation: 'approach'
                },
                {
                    description: '脱水缩合',
                    duration: 2000,
                    animation: 'dehydration'
                },
                {
                    description: '肽键形成',
                    duration: 1000,
                    animation: 'peptideBond'
                }
            ]
        };

        console.log(`已加载 ${Object.keys(this.reactions).length} 个化学反应`);
    }

    /**
     * 获取所有可用的反应
     * @returns {Object} 反应列表
     */
    getAvailableReactions() {
        return Object.keys(this.reactions).map(key => ({
            id: key,
            name: this.reactions[key].name,
            description: this.reactions[key].description,
            equation: this.reactions[key].equation,
            type: this.reactions[key].type
        }));
    }

    /**
     * 开始化学反应
     * @param {string} reactionId - 反应ID
     * @param {Object} options - 反应选项
     */
    startReaction(reactionId, options = {}) {
        if (this.isReacting) {
            console.warn('已有反应在进行中');
            return false;
        }

        const reaction = this.reactions[reactionId];
        if (!reaction) {
            console.error(`未找到反应: ${reactionId}`);
            return false;
        }

        console.log(`开始反应: ${reaction.name}`);

        this.currentReaction = reaction;
        this.isReacting = true;
        this.reactionProgress = 0;
        this.reactionSpeed = options.speed || 1.0;

        // 清除现有分子
        this.viewer.clearMolecule();

        // 设置反应场景
        this._setupReactionScene(reaction);

        // 开始反应动画
        this._animateReaction();

        return true;
    }

    /**
     * 停止当前反应
     */
    stopReaction() {
        if (!this.isReacting) return;

        console.log('停止反应');
        this.isReacting = false;
        this.currentReaction = null;
        this.reactionProgress = 0;

        // 清除反应粒子
        this._clearReactionParticles();
    }

    /**
     * 设置反应场景
     * @private
     * @param {Object} reaction - 反应数据
     */
    _setupReactionScene(reaction) {
        // 创建反应容器
        this.reactionGroup = new THREE.Group();
        this.viewer.scene.add(this.reactionGroup);

        console.log(`设置反应场景: ${reaction.name}`);
        console.log(`反应物: ${reaction.reactants.join(', ')}`);
        console.log(`产物: ${reaction.products.join(', ')}`);

        // 加载反应物分子
        this.reactantMolecules = [];
        reaction.reactants.forEach((moleculeId, index) => {
            console.log(`尝试加载反应物: ${moleculeId}`);
            const molecule = this.viewer.loader?.getMolecule(moleculeId);
            if (molecule) {
                console.log(`成功获取分子数据: ${molecule.name}`);
                const moleculeGroup = this._createMoleculeGroup(molecule);
                // 将反应物分子放置在不同位置
                moleculeGroup.position.set(
                    (index - reaction.reactants.length / 2) * 4,
                    0,
                    -2
                );
                this.reactionGroup.add(moleculeGroup);
                this.reactantMolecules.push(moleculeGroup);
                console.log(`反应物分子已添加到场景，位置: ${moleculeGroup.position.x}, ${moleculeGroup.position.y}, ${moleculeGroup.position.z}`);
            } else {
                console.error(`无法获取分子数据: ${moleculeId}`);
            }
        });

        // 预创建产物分子（初始时隐藏）
        this.productMolecules = [];
        reaction.products.forEach((moleculeId, index) => {
            console.log(`尝试加载产物: ${moleculeId}`);
            const molecule = this.viewer.loader?.getMolecule(moleculeId);
            if (molecule) {
                console.log(`成功获取产物数据: ${molecule.name}`);
                const moleculeGroup = this._createMoleculeGroup(molecule);
                moleculeGroup.position.set(
                    (index - reaction.products.length / 2) * 4,
                    0,
                    2
                );
                moleculeGroup.visible = false;
                this.reactionGroup.add(moleculeGroup);
                this.productMolecules.push(moleculeGroup);
                console.log(`产物分子已添加到场景，位置: ${moleculeGroup.position.x}, ${moleculeGroup.position.y}, ${moleculeGroup.position.z}`);
            } else {
                console.error(`无法获取产物数据: ${moleculeId}`);
            }
        });

        console.log(`反应场景设置完成，反应物数量: ${this.reactantMolecules.length}, 产物数量: ${this.productMolecules.length}`);
    }

    /**
     * 创建分子组
     * @private
     * @param {Object} molecule - 分子数据
     * @returns {THREE.Group} 分子组
     */
    _createMoleculeGroup(molecule) {
        const group = new THREE.Group();

        console.log(`创建分子组: ${molecule.name}, 原子数量: ${molecule.atoms.length}`);

        // 渲染原子
        molecule.atoms.forEach((atom, index) => {
            const radius = this._getAtomRadius(atom.symbol) * 0.5; // 增大原子半径
            const geometry = new THREE.SphereGeometry(radius, 16, 16);
            const material = new THREE.MeshLambertMaterial({
                color: this._getAtomColor(atom.symbol),
                transparent: false,
                opacity: 1.0
            });

            const atomMesh = new THREE.Mesh(geometry, material);
            atomMesh.position.set(atom.position[0], atom.position[1], atom.position[2]);
            group.add(atomMesh);

            console.log(`添加原子 ${index}: ${atom.symbol} at (${atom.position[0]}, ${atom.position[1]}, ${atom.position[2]})`);
        });

        // 渲染化学键
        if (molecule.bonds) {
            molecule.bonds.forEach((bond, index) => {
                const atom1 = molecule.atoms[bond.atoms[0]];
                const atom2 = molecule.atoms[bond.atoms[1]];

                if (atom1 && atom2) {
                    const bondMesh = this._createBond(atom1.position, atom2.position, bond.order);
                    group.add(bondMesh);
                    console.log(`添加化学键 ${index}: ${bond.atoms[0]} - ${bond.atoms[1]}`);
                }
            });
        }

        // 确保分子组可见
        group.visible = true;

        console.log(`分子组创建完成，子对象数量: ${group.children.length}`);
        return group;
    }

    /**
     * 创建化学键
     * @private
     * @param {Array} pos1 - 第一个原子位置
     * @param {Array} pos2 - 第二个原子位置
     * @param {number} order - 键级
     * @returns {THREE.Mesh} 键的网格
     */
    _createBond(pos1, pos2, order = 1) {
        const start = new THREE.Vector3(pos1[0], pos1[1], pos1[2]);
        const end = new THREE.Vector3(pos2[0], pos2[1], pos2[2]);
        const direction = new THREE.Vector3().subVectors(end, start);
        const length = direction.length();

        const geometry = new THREE.CylinderGeometry(0.1, 0.1, length, 8);
        const material = new THREE.MeshLambertMaterial({
            color: 0x888888,
            transparent: false,
            opacity: 1.0
        });

        const bond = new THREE.Mesh(geometry, material);

        // 定位和旋转键
        bond.position.copy(start).add(end).multiplyScalar(0.5);
        bond.lookAt(end);
        bond.rotateX(Math.PI / 2);

        return bond;
    }

    /**
     * 获取原子半径
     * @private
     */
    _getAtomRadius(symbol) {
        const radii = {
            'H': 0.31, 'C': 0.76, 'N': 0.71, 'O': 0.66,
            'F': 0.57, 'P': 1.07, 'S': 1.05, 'Cl': 0.99,
            'Na': 1.86, 'Mg': 1.60, 'K': 2.27, 'Ca': 1.97
        };
        return radii[symbol] || 1.0;
    }

    /**
     * 获取原子颜色
     * @private
     */
    _getAtomColor(symbol) {
        const colors = {
            'H': 0xffffff, 'C': 0x909090, 'N': 0x3050f8,
            'O': 0xff0d0d, 'F': 0x90e050, 'P': 0xff8000,
            'S': 0xffff30, 'Cl': 0x1ff01f, 'Na': 0xab5cf2,
            'Mg': 0x8aff00, 'K': 0x8f40d4, 'Ca': 0x3dff00
        };
        return colors[symbol] || 0xff69b4;
    }

    /**
     * 动画化反应过程
     * @private
     */
    _animateReaction() {
        if (!this.isReacting || !this.currentReaction) return;

        const reaction = this.currentReaction;
        const totalDuration = reaction.duration / this.reactionSpeed;

        // 计算当前步骤
        let currentTime = 0;
        let currentStepIndex = 0;

        for (let i = 0; i < reaction.steps.length; i++) {
            const stepDuration = reaction.steps[i].duration / this.reactionSpeed;
            if (this.reactionProgress * totalDuration < currentTime + stepDuration) {
                currentStepIndex = i;
                break;
            }
            currentTime += stepDuration;
        }

        // 执行当前步骤的动画
        const currentStep = reaction.steps[currentStepIndex];
        this._executeStepAnimation(currentStep, currentStepIndex);

        // 更新粒子
        this._updateParticles();

        // 更新进度
        this.reactionProgress += 0.016 / (totalDuration / 1000); // 假设60fps

        if (this.reactionProgress >= 1.0) {
            this._completeReaction();
        } else {
            requestAnimationFrame(() => this._animateReaction());
        }
    }

    /**
     * 更新粒子系统
     * @private
     */
    _updateParticles() {
        for (let i = this.reactionParticles.length - 1; i >= 0; i--) {
            const particle = this.reactionParticles[i];

            // 更新粒子位置
            if (particle.velocity) {
                particle.position.add(particle.velocity);
                particle.velocity.y -= 0.002; // 重力效果
            }

            // 更新粒子生命周期
            if (particle.life !== undefined) {
                particle.life -= 0.02;
                particle.material.opacity = particle.life;

                // 粒子缩放效果
                const scale = particle.life;
                particle.scale.set(scale, scale, scale);

                // 移除死亡的粒子
                if (particle.life <= 0) {
                    this.reactionGroup.remove(particle);
                    this.reactionParticles.splice(i, 1);
                }
            }
        }
    }

    /**
     * 执行步骤动画
     * @private
     */
    _executeStepAnimation(step, stepIndex) {
        switch (step.animation) {
            case 'approach':
                this._animateApproach();
                break;
            case 'bondBreaking':
                this._animateBondBreaking();
                break;
            case 'separation':
                this._animateSeparation();
                break;
            case 'mixing':
                this._animateMixing();
                break;
            case 'activation':
                this._animateActivation();
                break;
            case 'rearrangement':
                this._animateRearrangement();
                break;
            case 'formation':
                this._animateFormation();
                break;
            // 酯化反应特有动画
            case 'orientation':
                this._animateApproach(); // 使用接近动画
                break;
            case 'protonTransfer':
                this._animateRearrangement(); // 使用重排动画
                break;
            case 'elimination':
                this._animateBondBreaking(); // 使用键断裂动画
                break;
            case 'esterBond':
                this._animateFormation(); // 使用形成动画
                break;
            // 肽键形成反应特有动画
            case 'dehydration':
                this._animateRearrangement(); // 使用重排动画
                break;
            case 'peptideBond':
                this._animateFormation(); // 使用形成动画
                break;
            default:
                console.warn(`未知的动画类型: ${step.animation}`);
                this._animateApproach(); // 默认使用接近动画
                break;
        }
    }

    /**
     * 分子接近动画
     * @private
     */
    _animateApproach() {
        this.reactantMolecules.forEach((molecule, index) => {
            const targetX = (index - this.reactantMolecules.length / 2) * 1.5;
            molecule.position.x += (targetX - molecule.position.x) * 0.02;
            molecule.position.z += (0 - molecule.position.z) * 0.02;

            // 添加轻微的振动效果
            molecule.rotation.x += Math.sin(Date.now() * 0.005 + index) * 0.01;
            molecule.rotation.y += Math.cos(Date.now() * 0.005 + index) * 0.01;
        });
    }

    /**
     * 键断裂动画
     * @private
     */
    _animateBondBreaking() {
        // 创建反应粒子效果
        if (Math.random() < 0.1) {
            this._createReactionParticle();
        }

        // 分子振动效果
        this.reactantMolecules.forEach(molecule => {
            molecule.rotation.x += (Math.random() - 0.5) * 0.1;
            molecule.rotation.y += (Math.random() - 0.5) * 0.1;
            molecule.rotation.z += (Math.random() - 0.5) * 0.1;
        });
    }

    /**
     * 产物分离动画
     * @private
     */
    _animateSeparation() {
        // 隐藏反应物，显示产物
        this.reactantMolecules.forEach(molecule => {
            molecule.visible = false;
        });

        this.productMolecules.forEach(molecule => {
            molecule.visible = true;
        });
    }

    /**
     * 创建反应粒子
     * @private
     */
    _createReactionParticle() {
        const geometry = new THREE.SphereGeometry(0.05, 8, 8);
        const material = new THREE.MeshBasicMaterial({
            color: Math.random() > 0.5 ? 0x00ffff : 0xff00ff,
            transparent: true,
            opacity: 1.0
        });

        const particle = new THREE.Mesh(geometry, material);
        particle.position.set(
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2
        );

        particle.velocity = new THREE.Vector3(
            (Math.random() - 0.5) * 0.1,
            Math.random() * 0.1,
            (Math.random() - 0.5) * 0.1
        );

        particle.life = 1.0;

        this.reactionGroup.add(particle);
        this.reactionParticles.push(particle);
    }

    /**
     * 完成反应
     * @private
     */
    _completeReaction() {
        console.log(`反应完成: ${this.currentReaction.name}`);
        this.isReacting = false;

        // 清除反应粒子
        setTimeout(() => {
            this._clearReactionParticles();
        }, 2000);
    }

    /**
     * 清除反应粒子
     * @private
     */
    _clearReactionParticles() {
        this.reactionParticles.forEach(particle => {
            this.reactionGroup.remove(particle);
        });
        this.reactionParticles = [];
    }

    /**
     * 混合动画
     * @private
     */
    _animateMixing() {
        this.reactantMolecules.forEach((molecule, index) => {
            // 创建螺旋运动
            const time = Date.now() * 0.003;
            const radius = 1.5;
            const angle = time + index * Math.PI;

            molecule.position.x = Math.cos(angle) * radius;
            molecule.position.z = Math.sin(angle) * radius;
            molecule.position.y = Math.sin(time * 2) * 0.5;

            // 旋转分子
            molecule.rotation.y += 0.05;
            molecule.rotation.x += 0.02;

            // 创建更多粒子
            if (Math.random() < 0.3) {
                this._createReactionParticle();
            }
        });
    }

    /**
     * 激活动画
     * @private
     */
    _animateActivation() {
        this.reactantMolecules.forEach((molecule, index) => {
            // 强烈振动表示激活能
            const intensity = 0.3;
            molecule.position.x += (Math.random() - 0.5) * intensity;
            molecule.position.y += (Math.random() - 0.5) * intensity;
            molecule.position.z += (Math.random() - 0.5) * intensity;

            // 快速旋转
            molecule.rotation.x += (Math.random() - 0.5) * 0.3;
            molecule.rotation.y += (Math.random() - 0.5) * 0.3;
            molecule.rotation.z += (Math.random() - 0.5) * 0.3;

            // 创建大量粒子表示能量释放
            if (Math.random() < 0.5) {
                this._createEnergyParticle();
            }
        });
    }

    /**
     * 重排动画
     * @private
     */
    _animateRearrangement() {
        this.reactantMolecules.forEach((molecule, index) => {
            // 分子变形效果
            const time = Date.now() * 0.01;
            const scale = 1 + Math.sin(time + index) * 0.2;
            molecule.scale.set(scale, scale, scale);

            // 复杂的旋转模式
            molecule.rotation.x += Math.sin(time * 0.7) * 0.05;
            molecule.rotation.y += Math.cos(time * 0.5) * 0.05;
            molecule.rotation.z += Math.sin(time * 0.3) * 0.05;

            // 位置微调
            molecule.position.y += Math.sin(time + index * 2) * 0.02;

            // 创建重排粒子
            if (Math.random() < 0.2) {
                this._createRearrangementParticle();
            }
        });
    }

    /**
     * 形成动画
     * @private
     */
    _animateFormation() {
        // 隐藏反应物，显示产物
        this.reactantMolecules.forEach(molecule => {
            molecule.visible = false;
        });

        this.productMolecules.forEach((molecule, index) => {
            molecule.visible = true;

            // 产物从中心向外扩散
            const targetX = (index - this.productMolecules.length / 2) * 3;
            molecule.position.x += (targetX - molecule.position.x) * 0.05;

            // 添加庆祝效果
            molecule.rotation.y += 0.1;

            // 创建庆祝粒子
            if (Math.random() < 0.4) {
                this._createCelebrationParticle();
            }
        });
    }

    /**
     * 创建能量粒子
     * @private
     */
    _createEnergyParticle() {
        const geometry = new THREE.SphereGeometry(0.08, 8, 8);
        const material = new THREE.MeshBasicMaterial({
            color: 0xff4444,
            transparent: true,
            opacity: 1.0
        });

        const particle = new THREE.Mesh(geometry, material);
        particle.position.set(
            (Math.random() - 0.5) * 3,
            (Math.random() - 0.5) * 3,
            (Math.random() - 0.5) * 3
        );

        particle.velocity = new THREE.Vector3(
            (Math.random() - 0.5) * 0.2,
            Math.random() * 0.2,
            (Math.random() - 0.5) * 0.2
        );

        particle.life = 1.0;

        this.reactionGroup.add(particle);
        this.reactionParticles.push(particle);
    }

    /**
     * 创建重排粒子
     * @private
     */
    _createRearrangementParticle() {
        const geometry = new THREE.SphereGeometry(0.06, 8, 8);
        const material = new THREE.MeshBasicMaterial({
            color: 0x44ff44,
            transparent: true,
            opacity: 1.0
        });

        const particle = new THREE.Mesh(geometry, material);
        particle.position.set(
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2
        );

        particle.velocity = new THREE.Vector3(
            (Math.random() - 0.5) * 0.15,
            Math.random() * 0.15,
            (Math.random() - 0.5) * 0.15
        );

        particle.life = 1.0;

        this.reactionGroup.add(particle);
        this.reactionParticles.push(particle);
    }

    /**
     * 创建庆祝粒子
     * @private
     */
    _createCelebrationParticle() {
        const geometry = new THREE.SphereGeometry(0.04, 8, 8);
        const material = new THREE.MeshBasicMaterial({
            color: Math.random() > 0.5 ? 0xffff44 : 0x44ffff,
            transparent: true,
            opacity: 1.0
        });

        const particle = new THREE.Mesh(geometry, material);
        particle.position.set(
            (Math.random() - 0.5) * 4,
            Math.random() * 2,
            (Math.random() - 0.5) * 4
        );

        particle.velocity = new THREE.Vector3(
            (Math.random() - 0.5) * 0.1,
            Math.random() * 0.3,
            (Math.random() - 0.5) * 0.1
        );

        particle.life = 1.0;

        this.reactionGroup.add(particle);
        this.reactionParticles.push(particle);
    }
}
