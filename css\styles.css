/* 全局样式 - 增强版 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --accent-color: #e74c3c;
    --dark-bg: #2c3e50;
    --light-bg: #f5f5f5;
    --text-color: #333;
    --light-text: #fff;
    --border-radius: 8px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

body {
    font-family: 'Segoe UI', 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-bg);
    overflow-x: hidden;
}

header {
    background: linear-gradient(135deg, #2c3e50, #1a2530);
    color: var(--light-text);
    text-align: center;
    padding: 1.5rem 0;
    box-shadow: var(--box-shadow);
    position: relative;
}

header h1 {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

header .subtitle {
    font-size: 1rem;
    opacity: 0.8;
    font-weight: 300;
    letter-spacing: 1px;
}

footer {
    background: linear-gradient(135deg, #2c3e50, #1a2530);
    color: var(--light-text);
    text-align: center;
    padding: 1rem 0;
    position: fixed;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
}

footer p {
    margin: 0 auto;
}

.footer-links {
    position: absolute;
    right: 20px;
}

.footer-links a {
    color: var(--light-text);
    margin-left: 15px;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.footer-links a:hover {
    opacity: 1;
    text-decoration: underline;
}

.container {
    display: flex;
    height: calc(100vh - 140px);
    padding: 20px;
    max-width: 1800px;
    margin: 0 auto;
}

/* 控制面板样式 */
.control-panel {
    width: 320px;
    background-color: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow-y: auto;
    transition: all 0.3s ease;
}

.control-panel h2 {
    color: var(--dark-bg);
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

.control-group {
    margin-bottom: 25px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-bg);
    font-size: 0.95rem;
}

select, input[type="text"], input[type="number"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 0.95rem;
    transition: all 0.3s;
    color: var(--text-color);
}

/* 输入框和按钮组合 */
.input-with-button {
    display: flex;
    gap: 8px;
}

.input-with-button input {
    flex: 3;
}

.input-with-button button {
    flex: 1;
}

.help-text {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

select:focus, input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* 美化下拉菜单 */
select {
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6"><path d="M0 0l6 6 6-6z" fill="%23666"/></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 30px;
}

optgroup {
    font-weight: bold;
    color: var(--dark-bg);
}

/* 按钮样式 */
.button-row {
    display: flex;
    gap: 10px;
}

button {
    padding: 10px 15px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
    font-size: 0.95rem;
}

.primary-button {
    background-color: var(--primary-color);
    color: white;
    flex: 2;
}

.primary-button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.secondary-button {
    background-color: #f1f1f1;
    color: var(--text-color);
    flex: 1;
}

.secondary-button:hover {
    background-color: #e1e1e1;
    transform: translateY(-2px);
}

/* 滑块样式 */
input[type="range"] {
    -webkit-appearance: none;
    width: 100%;
    height: 6px;
    border-radius: 5px;
    background: #ddd;
    outline: none;
    margin: 10px 0;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    transition: all 0.2s;
}

input[type="range"]::-webkit-slider-thumb:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

/* 开关样式 */
.toggle-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 5px;
}

.toggle-container span {
    font-size: 0.85rem;
    color: #666;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin: 0 10px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.info-panel {
    margin-top: 25px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.info-panel h3 {
    color: var(--dark-bg);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.info-panel p {
    font-size: 0.9rem;
    line-height: 1.5;
}

.info-panel .error {
    color: #e74c3c;
    font-weight: bold;
}

.info-panel a {
    color: var(--primary-color);
    text-decoration: none;
}

.info-panel a:hover {
    text-decoration: underline;
}

/* 可视化区域样式 */
.viewer-container {
    flex: 1;
    margin-left: 25px;
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

#molecule-viewer {
    width: 100%;
    height: 100%;
    background: #000000; /* 设置为纯黑色 */
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* 对话框样式 */
.dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.dialog-content {
    background-color: white;
    padding: 25px;
    border-radius: var(--border-radius);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.close-button {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    transition: color 0.3s;
}

.close-button:hover {
    color: var(--accent-color);
}

.dialog h2 {
    color: var(--dark-bg);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color);
}

.dialog h3 {
    color: var(--dark-bg);
    margin: 20px 0 10px;
    font-size: 1.1rem;
}

.dialog p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.dialog ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.dialog li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.dialog.active {
    display: flex;
    animation: fadeIn 0.3s ease-out;
}

.dialog.active .dialog-content {
    animation: slideIn 0.3s ease-out;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .container {
        flex-direction: column;
        height: auto;
        padding: 15px;
    }

    .control-panel {
        width: 100%;
        margin-bottom: 20px;
        max-height: 50vh;
    }

    .viewer-container {
        margin-left: 0;
        height: 60vh;
        margin-bottom: 60px;
    }
}

@media (max-width: 576px) {
    header h1 {
        font-size: 1.8rem;
    }

    header .subtitle {
        font-size: 0.9rem;
    }

    .control-panel {
        padding: 15px;
    }

    .button-row {
        flex-direction: column;
        gap: 8px;
    }

    .primary-button, .secondary-button {
        width: 100%;
    }

    .viewer-container {
        height: 50vh;
    }

    .dialog-content {
        padding: 15px;
        width: 95%;
    }
}
