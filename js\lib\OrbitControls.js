// OrbitControls.js - 简化版
// 基于Three.js的OrbitControls

// 确保THREE已定义
if (typeof THREE === 'undefined') {
    console.error('THREE未定义，请确保在OrbitControls.js之前加载three.js');
}

THREE.OrbitControls = function(camera, domElement) {
    this.camera = camera;
    this.domElement = domElement || document;
    
    // API
    this.enabled = true;
    this.target = new THREE.Vector3();
    
    this.enableZoom = true;
    this.zoomSpeed = 1.0;
    
    this.enableRotate = true;
    this.rotateSpeed = 1.0;
    
    this.enablePan = true;
    this.panSpeed = 1.0;
    
    this.enableDamping = false;
    this.dampingFactor = 0.05;
    
    this.minDistance = 0;
    this.maxDistance = Infinity;
    
    this.minPolarAngle = 0; // 弧度
    this.maxPolarAngle = Math.PI; // 弧度
    
    this.minAzimuthAngle = -Infinity; // 弧度
    this.maxAzimuthAngle = Infinity; // 弧度
    
    // 鼠标按钮
    this.mouseButtons = { LEFT: 0, MIDDLE: 1, RIGHT: 2 };
    
    // 当前状态
    this.state = {
        NONE: -1,
        ROTATE: 0,
        DOLLY: 1,
        PAN: 2,
        TOUCH_ROTATE: 3,
        TOUCH_DOLLY: 4,
        TOUCH_PAN: 5
    };
    
    let scope = this;
    let STATE = scope.state.NONE;
    
    let EPS = 0.000001;
    
    // 当前位置
    let spherical = new THREE.Spherical();
    let sphericalDelta = new THREE.Spherical();
    
    let scale = 1;
    let panOffset = new THREE.Vector3();
    let zoomChanged = false;
    
    let rotateStart = new THREE.Vector2();
    let rotateEnd = new THREE.Vector2();
    let rotateDelta = new THREE.Vector2();
    
    let panStart = new THREE.Vector2();
    let panEnd = new THREE.Vector2();
    let panDelta = new THREE.Vector2();
    
    let dollyStart = new THREE.Vector2();
    let dollyEnd = new THREE.Vector2();
    let dollyDelta = new THREE.Vector2();
    
    // 事件处理函数
    function onMouseDown(event) {
        if (scope.enabled === false) return;
        
        event.preventDefault();
        
        switch (event.button) {
            case scope.mouseButtons.LEFT:
                if (event.ctrlKey || event.metaKey) {
                    STATE = scope.state.PAN;
                } else {
                    STATE = scope.state.ROTATE;
                }
                rotateStart.set(event.clientX, event.clientY);
                break;
            case scope.mouseButtons.MIDDLE:
                STATE = scope.state.DOLLY;
                dollyStart.set(event.clientX, event.clientY);
                break;
            case scope.mouseButtons.RIGHT:
                STATE = scope.state.PAN;
                panStart.set(event.clientX, event.clientY);
                break;
        }
        
        document.addEventListener('mousemove', onMouseMove, false);
        document.addEventListener('mouseup', onMouseUp, false);
    }
    
    function onMouseMove(event) {
        if (scope.enabled === false) return;
        
        event.preventDefault();
        
        switch (STATE) {
            case scope.state.ROTATE:
                rotateEnd.set(event.clientX, event.clientY);
                rotateDelta.subVectors(rotateEnd, rotateStart);
                
                // 旋转
                sphericalDelta.theta -= 2 * Math.PI * rotateDelta.x / scope.domElement.clientHeight * scope.rotateSpeed;
                sphericalDelta.phi -= 2 * Math.PI * rotateDelta.y / scope.domElement.clientHeight * scope.rotateSpeed;
                
                rotateStart.copy(rotateEnd);
                break;
            case scope.state.DOLLY:
                dollyEnd.set(event.clientX, event.clientY);
                dollyDelta.subVectors(dollyEnd, dollyStart);
                
                if (dollyDelta.y > 0) {
                    scale /= Math.pow(0.95, scope.zoomSpeed);
                } else if (dollyDelta.y < 0) {
                    scale *= Math.pow(0.95, scope.zoomSpeed);
                }
                
                dollyStart.copy(dollyEnd);
                break;
            case scope.state.PAN:
                panEnd.set(event.clientX, event.clientY);
                panDelta.subVectors(panEnd, panStart);
                
                // 平移
                pan(panDelta.x, panDelta.y);
                
                panStart.copy(panEnd);
                break;
        }
    }
    
    function onMouseUp() {
        document.removeEventListener('mousemove', onMouseMove, false);
        document.removeEventListener('mouseup', onMouseUp, false);
        
        STATE = scope.state.NONE;
    }
    
    function onMouseWheel(event) {
        if (scope.enabled === false || scope.enableZoom === false) return;
        
        event.preventDefault();
        
        if (event.deltaY < 0) {
            scale /= Math.pow(0.95, scope.zoomSpeed);
        } else if (event.deltaY > 0) {
            scale *= Math.pow(0.95, scope.zoomSpeed);
        }
        
        zoomChanged = true;
    }
    
    function pan(deltaX, deltaY) {
        let offset = new THREE.Vector3();
        
        let element = scope.domElement === document ? scope.domElement.body : scope.domElement;
        
        if (scope.camera.isPerspectiveCamera) {
            // 透视相机
            let position = scope.camera.position;
            offset.copy(position).sub(scope.target);
            let targetDistance = offset.length();
            
            // 平移速度基于距离
            targetDistance *= Math.tan((scope.camera.fov / 2) * Math.PI / 180.0);
            
            // 左右平移
            pan_left_right(2 * deltaX * targetDistance / element.clientHeight * scope.panSpeed);
            
            // 上下平移
            pan_up_down(2 * deltaY * targetDistance / element.clientHeight * scope.panSpeed);
        } else if (scope.camera.isOrthographicCamera) {
            // 正交相机
            // 左右平移
            pan_left_right(deltaX * (scope.camera.right - scope.camera.left) / scope.camera.zoom / element.clientWidth * scope.panSpeed);
            
            // 上下平移
            pan_up_down(deltaY * (scope.camera.top - scope.camera.bottom) / scope.camera.zoom / element.clientHeight * scope.panSpeed);
        }
    }
    
    function pan_left_right(distance) {
        let v = new THREE.Vector3();
        v.setFromMatrixColumn(scope.camera.matrix, 0); // 获取相机的右向量
        v.multiplyScalar(-distance);
        
        panOffset.add(v);
    }
    
    function pan_up_down(distance) {
        let v = new THREE.Vector3();
        
        if (scope.camera.isPerspectiveCamera) {
            v.setFromMatrixColumn(scope.camera.matrix, 1); // 获取相机的上向量
        } else {
            v.setFromMatrixColumn(scope.camera.matrix, 1);
            v.normalize();
        }
        
        v.multiplyScalar(distance);
        
        panOffset.add(v);
    }
    
    // 事件监听器
    scope.domElement.addEventListener('contextmenu', function(event) { event.preventDefault(); }, false);
    scope.domElement.addEventListener('mousedown', onMouseDown, false);
    scope.domElement.addEventListener('wheel', onMouseWheel, false);
    
    // 公共方法
    this.update = function() {
        let offset = new THREE.Vector3();
        
        // 获取相机位置
        let position = scope.camera.position;
        
        // 复制当前相机位置
        offset.copy(position).sub(scope.target);
        
        // 转换为球坐标
        spherical.setFromVector3(offset);
        
        // 应用旋转
        spherical.theta += sphericalDelta.theta;
        spherical.phi += sphericalDelta.phi;
        
        // 限制角度
        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi));
        spherical.makeSafe();
        
        // 应用缩放
        spherical.radius *= scale;
        
        // 限制距离
        spherical.radius = Math.max(scope.minDistance, Math.min(scope.maxDistance, spherical.radius));
        
        // 应用平移
        scope.target.add(panOffset);
        
        // 更新相机位置
        offset.setFromSpherical(spherical);
        position.copy(scope.target).add(offset);
        
        // 相机朝向目标
        scope.camera.lookAt(scope.target);
        
        // 重置变化量
        sphericalDelta.set(0, 0, 0);
        panOffset.set(0, 0, 0);
        scale = 1;
        
        return true;
    };
    
    this.dispose = function() {
        scope.domElement.removeEventListener('contextmenu', function(event) { event.preventDefault(); }, false);
        scope.domElement.removeEventListener('mousedown', onMouseDown, false);
        scope.domElement.removeEventListener('wheel', onMouseWheel, false);
        
        document.removeEventListener('mousemove', onMouseMove, false);
        document.removeEventListener('mouseup', onMouseUp, false);
    };
};

// 添加球坐标系
if (!THREE.Spherical) {
    THREE.Spherical = function(radius, phi, theta) {
        this.radius = (radius !== undefined) ? radius : 1.0;
        this.phi = (phi !== undefined) ? phi : 0; // 极角，从y轴正方向开始
        this.theta = (theta !== undefined) ? theta : 0; // 方位角，从z轴正方向开始
    };
    
    THREE.Spherical.prototype = {
        constructor: THREE.Spherical,
        
        set: function(radius, phi, theta) {
            this.radius = radius;
            this.phi = phi;
            this.theta = theta;
            return this;
        },
        
        setFromVector3: function(vec3) {
            this.radius = vec3.length();
            
            if (this.radius === 0) {
                this.theta = 0;
                this.phi = 0;
            } else {
                this.theta = Math.atan2(vec3.x, vec3.z);
                this.phi = Math.acos(Math.min(Math.max(vec3.y / this.radius, -1), 1));
            }
            
            return this;
        },
        
        makeSafe: function() {
            this.phi = Math.max(0.000001, Math.min(Math.PI - 0.000001, this.phi));
            return this;
        }
    };
}
